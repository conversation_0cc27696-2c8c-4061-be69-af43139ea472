# 代码重构指南

## 概述
本项目已经完成了模块化重构，将原来的大型组件拆分为可复用的小模块。这样做可以提高代码的可维护性、可读性和可复用性。

## 重构内容

### 🔧 Composables (可组合函数)
位置：`src/composables/`

| 文件 | 描述 | 主要功能 |
|------|------|----------|
| `useStorage.js` | 数据持久化 | localStorage的统一管理 |
| `useWalletGeneration.js` | 钱包生成 | 助记词和随机钱包生成逻辑 |
| `useWeb3.js` | Web3操作 | RPC连接、Gas价格、余额查询等 |
| `useFileHandler.js` | 文件处理 | 文件上传、解析、下载等操作 |

### 🛠️ Utils (工具函数)
位置：`src/utils/`

| 文件 | 描述 | 主要功能 |
|------|------|----------|
| `constants.js` | 常量配置 | 网络配置、默认值、UI配置等 |
| `formatters.js` | 格式化工具 | 地址、金额、时间等格式化 |
| `validators.js` | 验证工具 | 各种数据格式验证 |
| `helpers.js` | 辅助函数 | 延迟、复制、防抖节流等通用工具 |

### 🎨 共享样式
位置：`src/assets/shared.css`
- 通用组件样式
- 响应式布局类
- 主题色彩和间距定义

### 🧩 子组件
位置：`src/components/`

#### 通用组件 (`common/`)
- `ProgressBar.vue` - 进度条组件
- `StatusGrid.vue` - 状态网格显示
- `AddressDisplay.vue` - 地址显示组件
- `FileUpload.vue` - 文件上传组件

#### 网络组件 (`network/`)
- `NetworkSelector.vue` - 网络选择器
- `RpcTester.vue` - RPC连接测试

#### 钱包组件 (`wallet/`)
- `WalletPreview.vue` - 钱包预览组件

## 使用示例

### 1. 使用数据持久化
```javascript
import { useStorage } from '@/composables/useStorage'
import { STORAGE_KEYS, DEFAULT_VALUES } from '@/utils/constants'

const { clearStoredData } = useStorage(
  STORAGE_KEYS.WALLET_GENERATOR,
  {
    useMnemonic,
    numWallets,
    ethAmount
  },
  DEFAULT_VALUES.WALLET_GENERATOR
)
```

### 2. 使用钱包生成
```javascript
import { useWalletGeneration } from '@/composables/useWalletGeneration'

const { isGenerating, generateWallets } = useWalletGeneration()

const result = await generateWallets({
  useMnemonic: true,
  numWallets: 50,
  onProgress: (current, total) => {
    console.log(`进度: ${current}/${total}`)
  }
})
```

### 3. 使用Web3功能
```javascript
import { useWeb3 } from '@/composables/useWeb3'

const { 
  testRpcConnection, 
  getCurrentGasPrice, 
  loadWalletBalances 
} = useWeb3()

// 测试连接
await testRpcConnection('https://eth-mainnet.alchemyapi.io/...')

// 获取Gas价格
const gasPrice = await getCurrentGasPrice()
```

### 4. 使用子组件
```vue
<template>
  <!-- 进度条 -->
  <ProgressBar 
    :current="executedCount" 
    :total="walletData.length" 
    :message="statusMessage" 
  />
  
  <!-- 状态网格 -->
  <StatusGrid :items="statusItems" />
  
  <!-- 地址显示 -->
  <AddressDisplay 
    :address="wallet.address" 
    :show-copy-button="true" 
    @copy="handleCopy" 
  />
  
  <!-- 文件上传 -->
  <FileUpload 
    :accepted-types=".txt"
    description="支持 .txt 文件格式"
    @file-selected="handleFileSelected"
  />
</template>
```

### 5. 使用工具函数
```javascript
import { formatAddress, formatEthAmount } from '@/utils/formatters'
import { copyToClipboard, sleep } from '@/utils/helpers'
import { isValidPrivateKey, isValidAddress } from '@/utils/validators'

// 格式化地址
const shortAddress = formatAddress(fullAddress, 20)

// 复制到剪贴板
await copyToClipboard(text, '复制成功')

// 验证数据
if (isValidPrivateKey(privateKey)) {
  // 处理有效私钥
}
```

## 重构优势

### ✅ 代码复用
- 相同的逻辑可以在多个组件中复用
- 减少代码重复，降低维护成本

### ✅ 职责分离
- 每个模块有明确的职责
- 易于理解和修改

### ✅ 测试友好
- 小模块更容易编写单元测试
- 可以独立测试每个功能

### ✅ 维护性提升
- 修改某个功能时，只需要关注对应的模块
- 减少了意外修改其他功能的风险

### ✅ 性能优化
- 可以按需引入模块
- 支持代码分割和懒加载

## 下一步建议

1. **应用新结构**：将现有的大型组件重构为使用新的模块化结构
2. **添加类型支持**：考虑添加 TypeScript 支持以提供更好的类型安全
3. **编写测试**：为关键的composable和工具函数编写单元测试
4. **文档完善**：为每个模块添加详细的JSDoc注释
5. **持续优化**：根据实际使用情况继续优化和拆分

## 迁移步骤

1. 逐步将原组件中的逻辑迁移到对应的composable中
2. 替换原来的工具函数为utils中的标准化版本
3. 使用新的子组件替换重复的UI代码
4. 应用共享样式，减少样式代码重复
5. 测试功能完整性，确保迁移后功能正常

通过这样的重构，您的代码将变得更加模块化、可维护和可扩展。 
<template>
  <BaseCard v-if="results.length > 0" :title="title" variant="result">
    <template #actions>
      <button @click="$emit('clear')" class="btn btn-clear">
        🗑️清除记录
      </button>
    </template>
    
    <div class="results-list">
      <div 
        v-for="(result, index) in displayResults" 
        :key="index" 
        class="result-item"
        :class="{ success: result.success, failure: !result.success }"
      >
        <div class="result-info">
          <slot name="result-info" :result="result" :index="index">
            <!-- 默认显示地址 -->
            <AddressDisplay 
              v-if="result.address"
              :address="result.address" 
              :max-length="22"
              class="result-address"
            />
            <span class="result-status">{{ result.success ? '✅ 成功' : '❌ 失败' }}</span>
          </slot>
        </div>
        <div class="result-details">
          <slot name="result-details" :result="result" :index="index">
            <!-- 默认显示错误和交易哈希 -->
            <div v-if="result.error" class="error-msg">{{ result.error }}</div>
            <div v-if="result.txHash" class="tx-hash">
              交易哈希: {{ formatTxHash(result.txHash, 100) }}
            </div>
          </slot>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { computed } from 'vue'
import BaseCard from './BaseCard.vue'
import AddressDisplay from './AddressDisplay.vue'
import { formatTxHash } from '@/utils/formatters'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '📝 交易记录'
  },
  maxDisplay: {
    type: Number,
    default: 20
  }
})

const emit = defineEmits(['clear'])

const displayResults = computed(() => {
  return props.results.slice(-props.maxDisplay)
})
</script>

<style scoped>
.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  background: rgba(60, 60, 60, 0.6);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  border-left: 4px solid #666;
}

.result-item.success {
  border-left-color: #27ae60;
  background: rgba(40, 80, 40, 0.6);
}

.result-item.failure {
  border-left-color: #e53e3e;
  background: rgba(80, 40, 40, 0.6);
}

.result-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
  min-width: 0;
}

.result-address {
  flex: 1;
  min-width: 0;
}

.result-status {
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.result-details {
  font-size: 12px;
  color: #cccccc;
}

.tx-hash {
  font-family: 'Courier New', monospace;
  word-break: break-all;
  color: #cccccc;
  margin-top: 8px;
}

.error-msg {
  color: #e53e3e;
  font-weight: 500;
  margin-bottom: 6px;
}

.btn-clear {
  background: rgba(255, 99, 99, 0.1);
  color: #ff6363;
  border: 1px solid rgba(255, 99, 99, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-clear:hover {
  background: rgba(255, 99, 99, 0.2);
  border-color: rgba(255, 99, 99, 0.5);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .result-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .result-address {
    font-size: 11px;
    word-break: break-all;
  }
}
</style>
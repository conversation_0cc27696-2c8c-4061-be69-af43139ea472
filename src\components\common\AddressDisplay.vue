<template>
  <span 
    class="address" 
    :title="fullAddress"
    @click="handleClick"
  >
    <span class="address-full">{{ fullAddress }}</span>
    <span class="address-short">{{ formatAddress(fullAddress, maxLength) }}</span>
    <button 
      v-if="showCopyButton" 
      @click.stop="copyAddress" 
      class="copy-btn-small" 
      :title="'复制地址'"
    >
      复制
    </button>
  </span>
</template>

<script setup>
import { computed } from 'vue'
import { formatAddress } from '@/utils/formatters'
import { copyToClipboard } from '@/utils/helpers'

const props = defineProps({
  address: {
    type: String,
    required: true
  },
  maxLength: {
    type: Number,
    default: 22
  },
  showCopyButton: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'copy'])

const fullAddress = computed(() => props.address || '')

function handleClick() {
  if (props.clickable) {
    emit('click', props.address)
  }
}

async function copyAddress() {
  const success = await copyToClipboard(props.address, '地址已复制到剪贴板')
  if (success) {
    emit('copy', props.address)
  }
}
</script>

<style scoped>
.address {
  color: #ffffff;
  font-weight: 500;
  word-break: break-all;
  overflow-wrap: break-word;
  cursor: help;
  transition: color 0.2s ease;
  font-family: 'Courier New', monospace;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.address:hover {
  color: #6b9bd1;
}

.address.clickable {
  cursor: pointer;
}

/* 默认：超宽屏幕显示完整地址 */
.address-full {
  display: inline;
}

.address-short {
  display: none;
}

.copy-btn-small {
  background: rgba(100, 100, 100, 0.6);
  color: #ffffff;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.copy-btn-small:hover {
  background: rgba(140, 140, 140, 0.8);
  opacity: 1;
  transform: scale(1.05);
}

/* 当容器宽度较小时显示缩短版本 */
@media (max-width: 1600px) {
  .address-full {
    display: none;
  }
  
  .address-short {
    display: inline;
  }
}

/* 移动端强制显示缩短版本 */
@media (max-width: 768px) {
  .address-full {
    display: none !important;
  }
  
  .address-short {
    display: inline !important;
  }
}
</style> 
import { ref } from 'vue'

/**
 * 文件处理相关的composable
 */
export function useFileHandler() {
  const fileInput = ref(null)

  // 通用下载函数
  function downloadFile(content, filename, type = 'text/plain') {
    const blob = new Blob([content], { type })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 触发文件上传
  function triggerFileUpload() {
    if (fileInput.value) {
      fileInput.value.click()
    }
  }

  // 处理文件拖拽
  function handleFileDrop(event, processFunction) {
    const files = event.dataTransfer.files
    if (files.length > 0) {
      processFunction(files[0])
    }
  }

  // 处理文件上传
  function handleFileUpload(event, processFunction) {
    const file = event.target.files[0]
    if (file) {
      processFunction(file)
    }
  }

  // 读取文本文件内容
  function readTextFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        resolve(e.target.result)
      }
      reader.onerror = (e) => {
        reject(new Error('文件读取失败'))
      }
      reader.readAsText(file)
    })
  }

  // 验证私钥格式
  function isValidPrivateKey(privateKey) {
    const cleanKey = privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey
    return /^[0-9a-fA-F]{64}$/.test(cleanKey) && cleanKey !== '0'.repeat(64)
  }

  // 解析钱包文件内容
  async function parseWalletFile(file, web3Instance = null) {
    try {
      const content = await readTextFile(file)
      const lines = content.trim().split('\n').filter(line => line.trim().length > 0)
      const wallets = []

      for (const line of lines) {
        const trimmedLine = line.trim()
        const parts = trimmedLine.split(/\s+/)
        
        if (parts.length === 2) {
          // 格式1: 地址 私钥
          const [address, privateKey] = parts
          
          // 验证私钥格式
          if (isValidPrivateKey(privateKey)) {
            // 确保私钥有0x前缀
            const formattedPrivateKey = privateKey.startsWith('0x') ? privateKey : '0x' + privateKey
            wallets.push({ address, privateKey: formattedPrivateKey })
          } else {
            console.warn(`无效的私钥格式: ${privateKey}`)
          }
        } else if (parts.length === 1 && isValidPrivateKey(parts[0])) {
          // 格式2: 只有私钥
          const privateKey = parts[0]
          try {
            // 确保私钥有0x前缀
            const formattedPrivateKey = privateKey.startsWith('0x') ? privateKey : '0x' + privateKey
            
            // 从私钥推导地址
            if (web3Instance) {
              const account = web3Instance.eth.accounts.privateKeyToAccount(formattedPrivateKey)
              wallets.push({ address: account.address, privateKey: formattedPrivateKey })
            } else {
              console.warn(`无法处理私钥: ${privateKey} (Web3未初始化)`)
            }
          } catch (error) {
            console.warn(`从私钥推导地址失败: ${privateKey}`, error)
          }
        } else {
          console.warn(`无效的行格式: ${trimmedLine}`)
        }
      }

      return wallets
    } catch (error) {
      console.error('文件解析失败:', error)
      throw new Error('文件解析失败: ' + error.message)
    }
  }

  // 解析地址文件内容 (地址,金额格式)
  async function parseAddressFile(file, web3Instance = null) {
    try {
      const content = await readTextFile(file)
      const lines = content.trim().split('\n').filter(line => line.trim().length > 0)
      const addresses = []

      for (const line of lines) {
        const trimmedLine = line.trim()
        if (trimmedLine.includes(',')) {
          const [address, amount] = trimmedLine.split(',').map(s => s.trim())
          
          // 验证地址格式
          if (web3Instance && web3Instance.utils.isAddress(address)) {
            const amountNum = parseFloat(amount)
            if (!isNaN(amountNum) && amountNum > 0) {
              addresses.push({ 
                address: web3Instance.utils.toChecksumAddress(address), 
                amount: amountNum 
              })
            } else {
              console.warn(`无效的金额: ${amount}`)
            }
          } else {
            console.warn(`无效的地址格式: ${address}`)
          }
        } else {
          console.warn(`无效的行格式（需要逗号分隔）: ${trimmedLine}`)
        }
      }

      return addresses
    } catch (error) {
      console.error('地址文件解析失败:', error)
      throw new Error('地址文件解析失败: ' + error.message)
    }
  }

  return {
    fileInput,
    downloadFile,
    triggerFileUpload,
    handleFileDrop,
    handleFileUpload,
    readTextFile,
    isValidPrivateKey,
    parseWalletFile,
    parseAddressFile
  }
} 
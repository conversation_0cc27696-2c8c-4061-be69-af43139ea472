<template>
  <div v-if="addresses.length > 0" class="address-preview">
    <h4>📍 地址预览 ({{ addresses.length }} 个地址)</h4>
    <div class="preview-list">
      <div
        v-for="(item, index) in addresses"
        :key="index"
        class="preview-item"
      >
        <AddressDisplay
          :address="item.address"
          :max-length="20"
          class="address"
        />
        <span class="amount">{{ item.amount }} ETH</span>
      </div>
    </div>
    <div class="total-summary">
      <strong>总计: {{ totalAmount.toFixed(6) }} ETH</strong>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import AddressDisplay from './AddressDisplay.vue'

const props = defineProps({
  addresses: {
    type: Array,
    default: () => []
  }
})

const totalAmount = computed(() => {
  return props.addresses.reduce((sum, item) => sum + item.amount, 0)
})
</script>

<style scoped>
.address-preview {
  background: rgba(40, 80, 40, 0.6);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.address-preview h4 {
  color: #27ae60;
  margin-bottom: 10px;
}

.preview-list {
  background: rgba(20, 20, 20, 0.6);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 155, 209, 0.5) rgba(40, 40, 40, 0.3);
}

.preview-list::-webkit-scrollbar {
  width: 6px;
}

.preview-list::-webkit-scrollbar-track {
  background: rgba(40, 40, 40, 0.3);
  border-radius: 3px;
}

.preview-list::-webkit-scrollbar-thumb {
  background: rgba(107, 155, 209, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.preview-list::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 155, 209, 0.8);
}

.preview-item {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  gap: 15px;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(120, 120, 120, 0.3);
  font-size: 13px;
}

.preview-item:last-child {
  border-bottom: none;
}

.address {
  font-family: 'Courier New', monospace;
  color: #ffffff;
  font-weight: 500;
  word-break: break-all;
  overflow-wrap: break-word;
  cursor: help;
  transition: color 0.2s ease;
}

.address:hover {
  color: #6b9bd1;
}

.amount {
  color: #27ae60;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}



.total-summary {
  text-align: center;
  padding: 10px;
  background: rgba(107, 155, 209, 0.2);
  border-radius: 6px;
  color: #6b9bd1;
  font-size: 16px;
}

@media (max-width: 768px) {
  .preview-item {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }

  .address {
    grid-row: 1;
    font-size: 12px;
    word-break: break-all;
  }

  .amount {
    grid-row: 2;
    text-align: left;
    justify-self: start;
  }
}
</style>
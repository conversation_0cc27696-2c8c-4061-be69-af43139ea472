<template>
  <button 
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClass"
    @click="$emit('click', $event)"
  >
    <span v-if="loading && loadingText" class="button-text">{{ loadingText }}</span>
    <span v-else class="button-text">
      <slot></slot>
    </span>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'danger', 'warning', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  type: {
    type: String,
    default: 'button'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: ''
  },
  block: {
    type: <PERSON>olean,
    default: false
  },
  outlined: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  }
})

defineEmits(['click'])

const buttonClass = computed(() => [
  'btn',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-block': props.block,
    'btn-outlined': props.outlined,
    'btn-loading': props.loading
  }
])
</script>

<style scoped>
.btn {
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 尺寸样式 */
.btn-small {
  padding: 8px 16px;
  font-size: 12px;
}

.btn-medium {
  padding: 14px 35px;
  font-size: 16px;
}

.btn-large {
  padding: 18px 45px;
  font-size: 18px;
}

/* 变体样式 */
.btn-primary {
  background: linear-gradient(135deg, #6b9bd1 0%, #557ca6 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(107, 155, 209, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(107, 155, 209, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 101, 101, 0.4);
}

.btn-secondary {
  background: rgba(60, 60, 60, 0.8);
  color: #ffffff;
  border: 1px solid rgba(120, 120, 120, 0.5);
  box-shadow: none;
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(80, 80, 80, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #d68910 0%, #b5770e 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(214, 137, 16, 0.3);
}

.btn-warning:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(214, 137, 16, 0.4);
}

.btn-info {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-info:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

/* 轮廓样式 */
.btn-outlined {
  background: transparent !important;
  border: 2px solid;
  box-shadow: none !important;
}

.btn-outlined.btn-primary {
  border-color: #6b9bd1;
  color: #6b9bd1;
}

.btn-outlined.btn-success {
  border-color: #48bb78;
  color: #48bb78;
}

.btn-outlined.btn-danger {
  border-color: #f56565;
  color: #f56565;
}

.btn-outlined:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 块级按钮 */
.btn-block {
  width: 100%;
  display: flex;
}

/* 加载状态 */
.btn-loading {
  position: relative;
}

.button-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .btn-medium {
    padding: 12px 24px;
    font-size: 14px;
  }
  
  .btn-large {
    padding: 16px 32px;
    font-size: 16px;
  }
}
</style>
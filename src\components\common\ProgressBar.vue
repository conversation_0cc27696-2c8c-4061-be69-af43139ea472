<template>
  <div class="progress-section">
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
    </div>
    <p v-if="message">{{ message }}</p>
    <p v-if="showCount">{{ current }}/{{ total }}</p>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  current: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 100
  },
  message: {
    type: String,
    default: ''
  },
  showCount: {
    type: Boolean,
    default: true
  }
})

const progressPercent = computed(() => {
  if (props.total === 0) return 0
  return Math.round((props.current / props.total) * 100)
})
</script>

<style scoped>
.progress-section {
  text-align: center;
  color: #ffffff;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #333333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6b9bd1, #8db4e2);
  transition: width 0.3s;
  border-radius: 4px;
}
</style> 
<template>
  <div class="card" :class="[variant, customClass]">
    <div v-if="$slots.header || title" class="card-header">
      <h3 v-if="title">{{ title }}</h3>
      <slot name="header"></slot>
      <div v-if="$slots.actions" class="card-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <div class="card-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'config', 'status', 'result'].includes(value)
  },
  customClass: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.card {
  background: rgba(40, 40, 40, 0.9);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(60, 60, 60, 0.5);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

@media (max-width: 768px) {
  .card {
    padding: 20px;
    border-radius: 12px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}
</style>
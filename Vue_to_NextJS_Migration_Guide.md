# Vue.js 到 Next.js 迁移技术文档

## 1. 项目概览

### 1.1 当前 Vue 项目功能
**AK47Tool** 是一个基于 Vue 3 的以太坊钱包工具，主要功能包括：

- **钱包生成器**：支持助记词和随机模式生成多个钱包
- **批量分发**：支持合约分发和私钥逐笔分发ETH到多个地址
- **合约交互**：支持ETH归集、代币归集和循环交互功能

### 1.2 技术架构
- **框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **状态管理**：Composables (无Vuex/Pinia)
- **样式**：原生CSS + 响应式设计
- **Web3库**：ethers.js + web3.js
- **路由**：单页面Tab切换（无vue-router）

## 2. 技术对比分析

### 2.1 组件系统对比

| 特性 | Vue 3 | Next.js (React) |
|------|-------|-----------------|
| 组件定义 | `<script setup>` | Function Component |
| 响应式数据 | `ref()`, `reactive()` | `useState()`, `useReducer()` |
| 生命周期 | `onMounted()`, `onUnmounted()` | `useEffect()` |
| 计算属性 | `computed()` | `useMemo()` |
| 事件处理 | `@click="handler"` | `onClick={handler}` |
| 条件渲染 | `v-if`, `v-show` | `{condition && <Component />}` |
| 列表渲染 | `v-for` | `array.map()` |
| 双向绑定 | `v-model` | `value + onChange` |

### 2.2 路由系统对比

| 特性 | Vue (当前项目) | Next.js |
|------|----------------|---------|
| 路由方式 | Tab切换 (单页面) | 文件系统路由 |
| 导航 | 状态切换 | `<Link>`, `useRouter()` |
| 动态路由 | 无 | `[id].js`, `[...slug].js` |
| 路由守卫 | 无 | Middleware |

### 2.3 状态管理对比

| 特性 | Vue (Composables) | Next.js |
|------|-------------------|---------|
| 全局状态 | Composables | Context API, Zustand, Redux |
| 本地状态 | `ref()`, `reactive()` | `useState()` |
| 持久化 | localStorage封装 | localStorage + useEffect |
| 状态共享 | 导入composable | Context Provider |

## 3. 目录结构映射

### 3.1 Vue 项目结构
```
src/
├── App.vue                 # 主应用组件
├── main.js                 # 应用入口
├── assets/                 # 静态资源
│   ├── base.css
│   ├── main.css
│   └── shared.css
├── components/             # 组件
│   ├── WalletGenerator.vue
│   ├── BatchDisperse.vue
│   ├── ContractInteraction.vue
│   ├── common/            # 通用组件
│   ├── layout/            # 布局组件
│   ├── network/           # 网络组件
│   └── wallet/            # 钱包组件
├── composables/           # 可组合函数
│   ├── useFileHandler.js
│   ├── useStorage.js
│   ├── useWalletGeneration.js
│   └── useWeb3.js
└── utils/                 # 工具函数
    ├── constants.js
    ├── formatters.js
    ├── helpers.js
    └── validators.js
```

### 3.2 Next.js 目标结构
```
src/
├── app/                   # App Router (Next.js 13+)
│   ├── layout.js         # 根布局 (替代App.vue)
│   ├── page.js           # 首页 (钱包生成器)
│   ├── disperse/
│   │   └── page.js       # 批量分发页面
│   ├── contract/
│   │   └── page.js       # 合约交互页面
│   └── globals.css       # 全局样式
├── components/            # React组件
│   ├── WalletGenerator.jsx
│   ├── BatchDisperse.jsx
│   ├── ContractInteraction.jsx
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   ├── network/          # 网络组件
│   └── wallet/           # 钱包组件
├── hooks/                # 自定义Hook (替代composables)
│   ├── useFileHandler.js
│   ├── useStorage.js
│   ├── useWalletGeneration.js
│   └── useWeb3.js
├── utils/                # 工具函数 (保持不变)
│   ├── constants.js
│   ├── formatters.js
│   ├── helpers.js
│   └── validators.js
└── styles/               # 样式文件
    ├── globals.css
    └── components.css
```

## 4. 代码迁移指南

### 4.1 Vue 组件到 React 组件

#### Vue 组件示例 (WalletGenerator.vue)
```vue
<script setup>
import { ref, computed } from 'vue'
import { useWalletGeneration } from '@/composables/useWalletGeneration'

const numWallets = ref(1)
const useMnemonic = ref(true)
const generatedData = ref({ addresses: [], privateKeys: [], mnemonic: '' })

const { isGenerating, generateWallets } = useWalletGeneration()

const statsItems = computed(() => [
  {
    label: '生成钱包数量',
    value: generatedData.value.addresses.length.toString(),
    status: 'success'
  }
])

async function handleGenerateWallets() {
  const result = await generateWallets({
    useMnemonic: useMnemonic.value,
    numWallets: numWallets.value
  })
  if (result) {
    generatedData.value = result
  }
}
</script>

<template>
  <div class="wallet-generator">
    <input v-model="numWallets" type="number" />
    <button @click="handleGenerateWallets" :disabled="isGenerating">
      {{ isGenerating ? '生成中...' : '生成钱包' }}
    </button>
    <div v-if="generatedData.addresses.length > 0">
      生成了 {{ statsItems[0].value }} 个钱包
    </div>
  </div>
</template>
```

#### Next.js 组件示例 (WalletGenerator.jsx)
```jsx
'use client'
import { useState, useMemo } from 'react'
import { useWalletGeneration } from '@/hooks/useWalletGeneration'

export default function WalletGenerator() {
  const [numWallets, setNumWallets] = useState(1)
  const [useMnemonic, setUseMnemonic] = useState(true)
  const [generatedData, setGeneratedData] = useState({ 
    addresses: [], 
    privateKeys: [], 
    mnemonic: '' 
  })

  const { isGenerating, generateWallets } = useWalletGeneration()

  const statsItems = useMemo(() => [
    {
      label: '生成钱包数量',
      value: generatedData.addresses.length.toString(),
      status: 'success'
    }
  ], [generatedData.addresses.length])

  const handleGenerateWallets = async () => {
    const result = await generateWallets({
      useMnemonic,
      numWallets
    })
    if (result) {
      setGeneratedData(result)
    }
  }

  return (
    <div className="wallet-generator">
      <input 
        value={numWallets} 
        onChange={(e) => setNumWallets(Number(e.target.value))} 
        type="number" 
      />
      <button onClick={handleGenerateWallets} disabled={isGenerating}>
        {isGenerating ? '生成中...' : '生成钱包'}
      </button>
      {generatedData.addresses.length > 0 && (
        <div>
          生成了 {statsItems[0].value} 个钱包
        </div>
      )}
    </div>
  )
}
```

### 4.2 Composables 到 Custom Hooks

#### Vue Composable 示例 (useWalletGeneration.js)
```javascript
import { ref } from 'vue'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const isGenerating = ref(false)
  const currentWallet = ref(0)

  async function generateWallets(options) {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating.value) return null
    
    isGenerating.value = true
    currentWallet.value = 0
    
    try {
      // 生成逻辑...
      return result
    } finally {
      isGenerating.value = false
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

#### Next.js Custom Hook 示例 (useWalletGeneration.js)
```javascript
import { useState } from 'react'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentWallet, setCurrentWallet] = useState(0)

  const generateWallets = async (options) => {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating) return null
    
    setIsGenerating(true)
    setCurrentWallet(0)
    
    try {
      // 生成逻辑...
      return result
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

### 4.3 Tab 导航到路由导航

#### Vue Tab 导航 (App.vue)
```vue
<script setup>
import { ref } from 'vue'

const activeTab = ref('wallet')

function switchTab(tab) {
  activeTab.value = tab
}
</script>

<template>
  <div class="tab-nav">
    <button
      @click="switchTab('wallet')"
      :class="['tab-btn', { active: activeTab === 'wallet' }]"
    >
      🔐 生成钱包
    </button>
    <button
      @click="switchTab('disperse')"
      :class="['tab-btn', { active: activeTab === 'disperse' }]"
    >
      💸 批量分发
    </button>
  </div>

  <div class="tab-content">
    <WalletGenerator v-if="activeTab === 'wallet'" />
    <BatchDisperse v-if="activeTab === 'disperse'" />
  </div>
</template>
```

#### Next.js 路由导航 (layout.js)
```jsx
'use client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function RootLayout({ children }) {
  const pathname = usePathname()

  return (
    <html lang="zh">
      <body>
        <div className="tab-nav">
          <Link
            href="/"
            className={`tab-btn ${pathname === '/' ? 'active' : ''}`}
          >
            🔐 生成钱包
          </Link>
          <Link
            href="/disperse"
            className={`tab-btn ${pathname === '/disperse' ? 'active' : ''}`}
          >
            💸 批量分发
          </Link>
        </div>

        <div className="tab-content">
          {children}
        </div>
      </body>
    </html>
  )
}
```

### 4.4 数据持久化迁移

#### Vue useStorage Composable
```javascript
import { ref, watch } from 'vue'

export function useStorage(key, data, defaultValues) {
  // 从localStorage加载数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem(key)
      if (stored) {
        const parsed = JSON.parse(stored)
        Object.keys(parsed).forEach(k => {
          if (data[k] && data[k].value !== undefined) {
            data[k].value = parsed[k]
          }
        })
      }
    } catch (error) {
      console.error('加载存储数据失败:', error)
    }
  }

  // 保存到localStorage
  const saveToStorage = () => {
    try {
      const toSave = {}
      Object.keys(data).forEach(k => {
        toSave[k] = data[k].value
      })
      localStorage.setItem(key, JSON.stringify(toSave))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 监听数据变化并自动保存
  Object.keys(data).forEach(k => {
    watch(data[k], saveToStorage, { deep: true })
  })

  loadFromStorage()

  return { saveToStorage, loadFromStorage }
}
```

#### Next.js useStorage Hook
```javascript
import { useState, useEffect } from 'react'

export function useStorage(key, initialData, defaultValues) {
  const [data, setData] = useState(initialData)

  // 从localStorage加载数据
  useEffect(() => {
    try {
      const stored = localStorage.getItem(key)
      if (stored) {
        const parsed = JSON.parse(stored)
        setData(prevData => ({ ...prevData, ...parsed }))
      }
    } catch (error) {
      console.error('加载存储数据失败:', error)
    }
  }, [key])

  // 保存到localStorage
  useEffect(() => {
    try {
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }, [key, data])

  const updateData = (updates) => {
    setData(prevData => ({ ...prevData, ...updates }))
  }

  const clearStoredData = () => {
    localStorage.removeItem(key)
    setData(defaultValues)
  }

  return { data, updateData, clearStoredData }
}
```

## 5. 配置与依赖迁移

### 5.1 构建配置对比

#### Vite 配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

export default defineConfig({
  plugins: [
    vue(),
    nodePolyfills({
      protocolImports: true,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['buffer', 'ethers']
  }
})
```

#### Next.js 配置 (next.config.js)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        buffer: require.resolve('buffer'),
      }
    }

    config.plugins.push(
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser',
      })
    )

    return config
  },
}

module.exports = nextConfig
```

### 5.2 依赖包迁移

#### Vue 项目依赖 (package.json)
```json
{
  "dependencies": {
    "@ethereumjs/wallet": "^10.0.0",
    "bip39": "^3.1.0",
    "buffer": "^6.0.3",
    "ethereumjs-wallet": "^1.0.2",
    "ethers": "^6.15.0",
    "vue": "^3.5.18",
    "web3": "^4.16.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^6.0.1",
    "vite": "^7.0.6",
    "vite-plugin-node-polyfills": "^0.24.0"
  }
}
```

#### Next.js 项目依赖 (package.json)
```json
{
  "dependencies": {
    "@ethereumjs/wallet": "^10.0.0",
    "bip39": "^3.1.0",
    "buffer": "^6.0.3",
    "crypto-browserify": "^3.12.0",
    "ethereumjs-wallet": "^1.0.2",
    "ethers": "^6.15.0",
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "stream-browserify": "^3.0.0",
    "web3": "^4.16.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "webpack": "^5.0.0"
  }
}
```

## 6. 环境变量与构建

### 6.1 环境变量配置

#### Vue 项目 (.env)
```bash
# Vue 环境变量以 VITE_ 开头
VITE_APP_TITLE=AK47Tool
VITE_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
VITE_DISPERSE_CONTRACT=0x123...
```

#### Next.js 项目 (.env.local)
```bash
# Next.js 环境变量以 NEXT_PUBLIC_ 开头（客户端可访问）
NEXT_PUBLIC_APP_TITLE=AK47Tool
NEXT_PUBLIC_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
NEXT_PUBLIC_DISPERSE_CONTRACT=0x123...

# 服务端环境变量（不需要前缀）
DATABASE_URL=postgresql://...
API_SECRET_KEY=your-secret-key
```

### 6.2 构建脚本对比

#### Vue 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

#### Next.js 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

## 7. 典型功能示例

### 7.1 完整组件迁移示例

#### Vue 组件 (BatchDisperse.vue 片段)
```vue
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useWeb3 } from '@/composables/useWeb3'
import { useStorage } from '@/composables/useStorage'

const privateKey = ref('')
const addressData = ref([])
const isRunning = ref(false)
const executedCount = ref(0)

const { web3, initWeb3, testRpcConnection } = useWeb3()

const totalAmount = computed(() => {
  return addressData.value.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
})

onMounted(() => {
  initWeb3('https://mainnet.infura.io/v3/your-key')
})

async function startDisperse() {
  if (!privateKey.value.trim()) {
    alert('请输入私钥')
    return
  }

  isRunning.value = true
  executedCount.value = 0

  try {
    for (let i = 0; i < addressData.value.length; i++) {
      const item = addressData.value[i]
      await sendTransaction(item.address, item.amount)
      executedCount.value++
    }
  } catch (error) {
    console.error('分发失败:', error)
  } finally {
    isRunning.value = false
  }
}

async function sendTransaction(to, amount) {
  // 发送交易逻辑
}
</script>

<template>
  <div class="batch-disperse">
    <div class="form-group">
      <label>私钥</label>
      <input v-model="privateKey" type="password" placeholder="输入私钥" />
    </div>

    <div class="stats">
      <p>总金额: {{ totalAmount }} ETH</p>
      <p>已执行: {{ executedCount }}/{{ addressData.length }}</p>
    </div>

    <button @click="startDisperse" :disabled="isRunning">
      {{ isRunning ? '执行中...' : '开始分发' }}
    </button>
  </div>
</template>
```

#### Next.js 组件 (BatchDisperse.jsx)
```jsx
'use client'
import { useState, useMemo, useEffect } from 'react'
import { useWeb3 } from '@/hooks/useWeb3'
import { useStorage } from '@/hooks/useStorage'

export default function BatchDisperse() {
  const [privateKey, setPrivateKey] = useState('')
  const [addressData, setAddressData] = useState([])
  const [isRunning, setIsRunning] = useState(false)
  const [executedCount, setExecutedCount] = useState(0)

  const { web3, initWeb3, testRpcConnection } = useWeb3()

  const totalAmount = useMemo(() => {
    return addressData.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
  }, [addressData])

  useEffect(() => {
    initWeb3('https://mainnet.infura.io/v3/your-key')
  }, [])

  const startDisperse = async () => {
    if (!privateKey.trim()) {
      alert('请输入私钥')
      return
    }

    setIsRunning(true)
    setExecutedCount(0)

    try {
      for (let i = 0; i < addressData.length; i++) {
        const item = addressData[i]
        await sendTransaction(item.address, item.amount)
        setExecutedCount(i + 1)
      }
    } catch (error) {
      console.error('分发失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const sendTransaction = async (to, amount) => {
    // 发送交易逻辑
  }

  return (
    <div className="batch-disperse">
      <div className="form-group">
        <label>私钥</label>
        <input
          value={privateKey}
          onChange={(e) => setPrivateKey(e.target.value)}
          type="password"
          placeholder="输入私钥"
        />
      </div>

      <div className="stats">
        <p>总金额: {totalAmount} ETH</p>
        <p>已执行: {executedCount}/{addressData.length}</p>
      </div>

      <button onClick={startDisperse} disabled={isRunning}>
        {isRunning ? '执行中...' : '开始分发'}
      </button>
    </div>
  )
}
```

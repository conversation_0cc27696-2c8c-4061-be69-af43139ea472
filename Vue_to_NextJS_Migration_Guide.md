# Vue.js 到 Next.js 迁移技术文档

## 1. 项目概览

### 1.1 当前 Vue 项目功能
**AK47Tool** 是一个基于 Vue 3 的以太坊钱包工具，主要功能包括：

- **钱包生成器**：支持助记词和随机模式生成多个钱包
- **批量分发**：支持合约分发和私钥逐笔分发ETH到多个地址
- **合约交互**：支持ETH归集、代币归集和循环交互功能

### 1.2 技术架构
- **框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **状态管理**：Composables (无Vuex/Pinia)
- **样式**：原生CSS + 响应式设计
- **Web3库**：ethers.js + web3.js
- **路由**：单页面Tab切换（无vue-router）

## 2. 技术对比分析

### 2.1 组件系统对比

| 特性 | Vue 3 | Next.js (React) |
|------|-------|-----------------|
| 组件定义 | `<script setup>` | Function Component |
| 响应式数据 | `ref()`, `reactive()` | `useState()`, `useReducer()` |
| 生命周期 | `onMounted()`, `onUnmounted()` | `useEffect()` |
| 计算属性 | `computed()` | `useMemo()` |
| 事件处理 | `@click="handler"` | `onClick={handler}` |
| 条件渲染 | `v-if`, `v-show` | `{condition && <Component />}` |
| 列表渲染 | `v-for` | `array.map()` |
| 双向绑定 | `v-model` | `value + onChange` |

### 2.2 路由系统对比

| 特性 | Vue (当前项目) | Next.js |
|------|----------------|---------|
| 路由方式 | Tab切换 (单页面) | 文件系统路由 |
| 导航 | 状态切换 | `<Link>`, `useRouter()` |
| 动态路由 | 无 | `[id].js`, `[...slug].js` |
| 路由守卫 | 无 | Middleware |

### 2.3 状态管理对比

| 特性 | Vue (Composables) | Next.js |
|------|-------------------|---------|
| 全局状态 | Composables | Context API, Zustand, Redux |
| 本地状态 | `ref()`, `reactive()` | `useState()` |
| 持久化 | localStorage封装 | localStorage + useEffect |
| 状态共享 | 导入composable | Context Provider |

## 3. 目录结构映射

### 3.1 Vue 项目结构
```
src/
├── App.vue                 # 主应用组件
├── main.js                 # 应用入口
├── assets/                 # 静态资源
│   ├── base.css
│   ├── main.css
│   └── shared.css
├── components/             # 组件
│   ├── WalletGenerator.vue
│   ├── BatchDisperse.vue
│   ├── ContractInteraction.vue
│   ├── common/            # 通用组件
│   ├── layout/            # 布局组件
│   ├── network/           # 网络组件
│   └── wallet/            # 钱包组件
├── composables/           # 可组合函数
│   ├── useFileHandler.js
│   ├── useStorage.js
│   ├── useWalletGeneration.js
│   └── useWeb3.js
└── utils/                 # 工具函数
    ├── constants.js
    ├── formatters.js
    ├── helpers.js
    └── validators.js
```

### 3.2 Next.js 目标结构
```
src/
├── app/                   # App Router (Next.js 13+)
│   ├── layout.js         # 根布局 (替代App.vue)
│   ├── page.js           # 首页 (钱包生成器)
│   ├── disperse/
│   │   └── page.js       # 批量分发页面
│   ├── contract/
│   │   └── page.js       # 合约交互页面
│   └── globals.css       # 全局样式
├── components/            # React组件
│   ├── WalletGenerator.jsx
│   ├── BatchDisperse.jsx
│   ├── ContractInteraction.jsx
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   ├── network/          # 网络组件
│   └── wallet/           # 钱包组件
├── hooks/                # 自定义Hook (替代composables)
│   ├── useFileHandler.js
│   ├── useStorage.js
│   ├── useWalletGeneration.js
│   └── useWeb3.js
├── utils/                # 工具函数 (保持不变)
│   ├── constants.js
│   ├── formatters.js
│   ├── helpers.js
│   └── validators.js
└── styles/               # 样式文件
    ├── globals.css
    └── components.css
```

## 4. 代码迁移指南

### 4.1 Vue 组件到 React 组件

#### Vue 组件示例 (WalletGenerator.vue)
```vue
<script setup>
import { ref, computed } from 'vue'
import { useWalletGeneration } from '@/composables/useWalletGeneration'

const numWallets = ref(1)
const useMnemonic = ref(true)
const generatedData = ref({ addresses: [], privateKeys: [], mnemonic: '' })

const { isGenerating, generateWallets } = useWalletGeneration()

const statsItems = computed(() => [
  {
    label: '生成钱包数量',
    value: generatedData.value.addresses.length.toString(),
    status: 'success'
  }
])

async function handleGenerateWallets() {
  const result = await generateWallets({
    useMnemonic: useMnemonic.value,
    numWallets: numWallets.value
  })
  if (result) {
    generatedData.value = result
  }
}
</script>

<template>
  <div class="wallet-generator">
    <input v-model="numWallets" type="number" />
    <button @click="handleGenerateWallets" :disabled="isGenerating">
      {{ isGenerating ? '生成中...' : '生成钱包' }}
    </button>
    <div v-if="generatedData.addresses.length > 0">
      生成了 {{ statsItems[0].value }} 个钱包
    </div>
  </div>
</template>
```

#### Next.js 组件示例 (WalletGenerator.jsx)
```jsx
'use client'
import { useState, useMemo } from 'react'
import { useWalletGeneration } from '@/hooks/useWalletGeneration'

export default function WalletGenerator() {
  const [numWallets, setNumWallets] = useState(1)
  const [useMnemonic, setUseMnemonic] = useState(true)
  const [generatedData, setGeneratedData] = useState({ 
    addresses: [], 
    privateKeys: [], 
    mnemonic: '' 
  })

  const { isGenerating, generateWallets } = useWalletGeneration()

  const statsItems = useMemo(() => [
    {
      label: '生成钱包数量',
      value: generatedData.addresses.length.toString(),
      status: 'success'
    }
  ], [generatedData.addresses.length])

  const handleGenerateWallets = async () => {
    const result = await generateWallets({
      useMnemonic,
      numWallets
    })
    if (result) {
      setGeneratedData(result)
    }
  }

  return (
    <div className="wallet-generator">
      <input 
        value={numWallets} 
        onChange={(e) => setNumWallets(Number(e.target.value))} 
        type="number" 
      />
      <button onClick={handleGenerateWallets} disabled={isGenerating}>
        {isGenerating ? '生成中...' : '生成钱包'}
      </button>
      {generatedData.addresses.length > 0 && (
        <div>
          生成了 {statsItems[0].value} 个钱包
        </div>
      )}
    </div>
  )
}
```

### 4.2 Composables 到 Custom Hooks

#### Vue Composable 示例 (useWalletGeneration.js)
```javascript
import { ref } from 'vue'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const isGenerating = ref(false)
  const currentWallet = ref(0)

  async function generateWallets(options) {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating.value) return null
    
    isGenerating.value = true
    currentWallet.value = 0
    
    try {
      // 生成逻辑...
      return result
    } finally {
      isGenerating.value = false
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

#### Next.js Custom Hook 示例 (useWalletGeneration.js)
```javascript
import { useState } from 'react'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentWallet, setCurrentWallet] = useState(0)

  const generateWallets = async (options) => {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating) return null
    
    setIsGenerating(true)
    setCurrentWallet(0)
    
    try {
      // 生成逻辑...
      return result
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

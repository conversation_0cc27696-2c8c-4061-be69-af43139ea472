# Vue.js 到 Next.js 迁移技术文档

## 1. 项目概览

### 1.1 当前 Vue 项目功能
**AK47Tool** 是一个基于 Vue 3 的以太坊钱包工具，主要功能包括：

#### 🔐 钱包生成器 (WalletGenerator)
- **助记词模式**：使用BIP39标准生成助记词，支持从同一助记词派生多个钱包
- **随机模式**：生成完全随机的独立钱包
- **批量生成**：支持1-10000个钱包的批量生成
- **进度显示**：实时显示生成进度和状态
- **数据导出**：支持导出地址列表、私钥列表、钱包数据文件
- **数据预览**：实时预览生成的地址和统计信息
- **数据持久化**：自动保存配置和生成结果到localStorage

#### 💸 批量分发 (BatchDisperse)
- **双模式分发**：
  - 合约分发：使用Disperse合约一次性分发到多个地址（Gas费用低）
  - 私钥逐笔分发：使用私钥向每个地址单独发送交易（不依赖合约）
- **网络支持**：支持多种EVM兼容网络（以太坊、BSC、Polygon等）
- **RPC配置**：支持自定义RPC端点和预设网络
- **Gas价格监控**：实时监控Gas价格，支持设置最大Gas价格阈值
- **文件导入**：支持导入地址,金额格式的txt文件
- **余额检查**：自动检查发送者余额是否足够
- **执行控制**：支持开始、停止、暂停分发操作
- **结果统计**：实时显示执行进度、成功/失败统计

#### 🤖 合约交互 (ContractInteraction)
- **三种交互模式**：
  - ETH归集：将多个钱包的ETH归集到指定地址
  - 代币归集：将多个钱包的ERC20代币归集到指定地址
  - 循环交互：按轮次循环执行交易（用于刷交互量）
- **钱包管理**：支持导入钱包数据文件，批量管理钱包
- **代币支持**：支持任意ERC20代币的归集操作
- **Gas优化**：支持设置Gas限制、最大Gas价格、额外Gas
- **智能重试**：支持nonce错误自动重试机制
- **余额查询**：批量查询钱包ETH和代币余额
- **交易记录**：详细记录每笔交易的结果和错误信息

### 1.2 技术架构
- **框架**：Vue 3 + Composition API
- **构建工具**：Vite + Node.js polyfills
- **状态管理**：Composables (无Vuex/Pinia)
- **样式**：原生CSS + 响应式设计 + 共享样式系统
- **Web3库**：ethers.js v6 + web3.js v4
- **路由**：单页面Tab切换（无vue-router）
- **数据持久化**：localStorage + 自动保存/恢复
- **文件处理**：原生File API + 文本解析
- **组件架构**：模块化组件 + 可复用子组件

### 1.3 详细组件结构

#### 🧩 通用组件 (common/)
- **BaseButton.vue**：统一按钮组件，支持多种变体、尺寸、加载状态
- **BaseCard.vue**：卡片容器组件，支持标题、操作按钮、多种变体
- **FormInput.vue**：表单输入组件，支持多种输入类型和验证
- **ProgressBar.vue**：进度条组件，显示任务执行进度
- **StatusGrid.vue**：状态网格组件，展示键值对状态信息
- **AddressDisplay.vue**：地址显示组件，支持地址截断和复制功能
- **AddressPreview.vue**：地址预览组件，批量显示地址和金额
- **DataInput.vue**：数据输入组件，支持文件上传和文本输入
- **FileUpload.vue**：文件上传组件，支持拖拽和文件类型限制
- **ActionButtonGroup.vue**：操作按钮组容器
- **FormatHelp.vue**：格式帮助组件，显示文件格式说明
- **FormatItem.vue**：格式项组件，显示具体格式示例
- **TransactionResults.vue**：交易结果组件，显示交易执行结果
- **WalletInfo.vue**：钱包信息组件，显示地址、余额等信息

#### 🏗️ 布局组件 (layout/)
- **BaseLayout.vue**：基础布局组件，支持单栏、双栏、三栏布局

#### 🌐 网络组件 (network/)
- **NetworkSelector.vue**：网络选择器，支持预设网络和自定义RPC
- **RpcTester.vue**：RPC连接测试器，测试连接状态和获取网络信息

#### 👛 钱包组件 (wallet/)
- **WalletPreview.vue**：钱包预览组件，显示钱包列表和余额信息

### 1.4 Composables功能详解

#### 🔧 useWalletGeneration.js
- **助记词生成**：使用ethers.js生成BIP39助记词
- **钱包派生**：从助记词派生指定数量的钱包
- **随机钱包生成**：生成完全随机的独立钱包
- **进度跟踪**：实时更新生成进度
- **私钥验证**：验证私钥格式的有效性

#### 🌐 useWeb3.js
- **RPC连接管理**：初始化和管理Web3连接
- **网络信息获取**：获取区块号、网络ID、Gas价格
- **余额查询**：批量查询ETH和ERC20代币余额
- **交易发送**：发送ETH转账和代币转账交易
- **合约交互**：与Disperse合约和ERC20合约交互
- **Gas价格监控**：实时获取和监控Gas价格

#### 💾 useStorage.js
- **数据持久化**：自动保存组件状态到localStorage
- **数据恢复**：页面刷新后自动恢复保存的状态
- **数据清除**：提供清除保存数据的功能
- **响应式同步**：监听数据变化并自动保存

#### 📁 useFileHandler.js
- **文件上传处理**：处理文件选择和读取
- **文本解析**：解析地址,金额格式的文本数据
- **文件下载**：生成和下载文本文件
- **数据验证**：验证导入数据的格式和有效性

### 1.5 工具函数详解

#### 📊 constants.js
- **网络配置**：预设的区块链网络RPC端点
- **默认值**：各组件的默认配置值
- **存储键**：localStorage的键名常量
- **UI配置**：界面相关的配置常量

#### 🎨 formatters.js
- **地址格式化**：地址截断显示
- **金额格式化**：ETH和代币金额的格式化显示
- **Gas价格格式化**：Gas价格的单位转换和显示
- **时间格式化**：时间戳的格式化显示

#### ✅ validators.js
- **地址验证**：验证以太坊地址格式
- **私钥验证**：验证私钥格式和有效性
- **金额验证**：验证数字金额的有效性
- **文件格式验证**：验证导入文件的格式

#### 🛠️ helpers.js
- **延迟函数**：异步延迟执行
- **复制功能**：复制文本到剪贴板
- **防抖节流**：函数执行频率控制
- **错误处理**：统一的错误信息处理

## 2. 技术对比分析

### 2.1 组件系统对比

| 特性 | Vue 3 | Next.js (React) |
|------|-------|-----------------|
| 组件定义 | `<script setup>` | Function Component |
| 响应式数据 | `ref()`, `reactive()` | `useState()`, `useReducer()` |
| 生命周期 | `onMounted()`, `onUnmounted()` | `useEffect()` |
| 计算属性 | `computed()` | `useMemo()` |
| 事件处理 | `@click="handler"` | `onClick={handler}` |
| 条件渲染 | `v-if`, `v-show` | `{condition && <Component />}` |
| 列表渲染 | `v-for` | `array.map()` |
| 双向绑定 | `v-model` | `value + onChange` |

### 2.2 路由系统对比

| 特性 | Vue (当前项目) | Next.js |
|------|----------------|---------|
| 路由方式 | Tab切换 (单页面) | 文件系统路由 |
| 导航 | 状态切换 | `<Link>`, `useRouter()` |
| 动态路由 | 无 | `[id].js`, `[...slug].js` |
| 路由守卫 | 无 | Middleware |

### 2.3 状态管理对比

| 特性 | Vue (Composables) | Next.js |
|------|-------------------|---------|
| 全局状态 | Composables | Context API, Zustand, Redux |
| 本地状态 | `ref()`, `reactive()` | `useState()` |
| 持久化 | localStorage封装 | localStorage + useEffect |
| 状态共享 | 导入composable | Context Provider |

## 3. 目录结构映射

### 3.1 Vue 项目结构（详细）
```
src/
├── App.vue                          # 主应用组件 - Tab导航和路由控制
├── main.js                          # 应用入口 - Vue应用初始化
├── assets/                          # 静态资源
│   ├── base.css                     # 基础样式重置
│   ├── main.css                     # 主要样式定义
│   └── shared.css                   # 共享组件样式
├── components/                      # 组件目录
│   ├── WalletGenerator.vue          # 钱包生成器主组件
│   ├── BatchDisperse.vue            # 批量分发主组件
│   ├── ContractInteraction.vue      # 合约交互主组件
│   ├── common/                      # 通用组件目录
│   │   ├── ActionButtonGroup.vue    # 操作按钮组
│   │   ├── AddressDisplay.vue       # 地址显示组件
│   │   ├── AddressPreview.vue       # 地址预览组件
│   │   ├── BaseButton.vue           # 基础按钮组件
│   │   ├── BaseCard.vue             # 基础卡片组件
│   │   ├── DataInput.vue            # 数据输入组件
│   │   ├── FileUpload.vue           # 文件上传组件
│   │   ├── FormInput.vue            # 表单输入组件
│   │   ├── FormatHelp.vue           # 格式帮助组件
│   │   ├── FormatItem.vue           # 格式项组件
│   │   ├── ProgressBar.vue          # 进度条组件
│   │   ├── StatusGrid.vue           # 状态网格组件
│   │   ├── TransactionResults.vue   # 交易结果组件
│   │   └── WalletInfo.vue           # 钱包信息组件
│   ├── layout/                      # 布局组件目录
│   │   └── BaseLayout.vue           # 基础布局组件
│   ├── network/                     # 网络相关组件
│   │   ├── NetworkSelector.vue      # 网络选择器
│   │   └── RpcTester.vue            # RPC连接测试器
│   └── wallet/                      # 钱包相关组件
│       └── WalletPreview.vue        # 钱包预览组件
├── composables/                     # 可组合函数目录
│   ├── useFileHandler.js            # 文件处理逻辑
│   ├── useStorage.js                # 数据持久化逻辑
│   ├── useWalletGeneration.js       # 钱包生成逻辑
│   └── useWeb3.js                   # Web3交互逻辑
└── utils/                           # 工具函数目录
    ├── constants.js                 # 常量配置
    ├── formatters.js                # 格式化工具
    ├── helpers.js                   # 辅助函数
    └── validators.js                # 验证工具
```

### 3.2 Next.js 目标结构（详细）
```
src/
├── app/                             # App Router (Next.js 13+)
│   ├── layout.js                    # 根布局 (替代App.vue) - 导航和全局布局
│   ├── page.js                      # 首页 (钱包生成器)
│   ├── disperse/
│   │   └── page.js                  # 批量分发页面
│   ├── contract/
│   │   └── page.js                  # 合约交互页面
│   └── globals.css                  # 全局样式
├── components/                      # React组件目录
│   ├── WalletGenerator.jsx          # 钱包生成器主组件
│   ├── BatchDisperse.jsx            # 批量分发主组件
│   ├── ContractInteraction.jsx      # 合约交互主组件
│   ├── common/                      # 通用组件目录
│   │   ├── ActionButtonGroup.jsx    # 操作按钮组
│   │   ├── AddressDisplay.jsx       # 地址显示组件
│   │   ├── AddressPreview.jsx       # 地址预览组件
│   │   ├── BaseButton.jsx           # 基础按钮组件
│   │   ├── BaseCard.jsx             # 基础卡片组件
│   │   ├── DataInput.jsx            # 数据输入组件
│   │   ├── FileUpload.jsx           # 文件上传组件
│   │   ├── FormInput.jsx            # 表单输入组件
│   │   ├── FormatHelp.jsx           # 格式帮助组件
│   │   ├── FormatItem.jsx           # 格式项组件
│   │   ├── ProgressBar.jsx          # 进度条组件
│   │   ├── StatusGrid.jsx           # 状态网格组件
│   │   ├── TransactionResults.jsx   # 交易结果组件
│   │   └── WalletInfo.jsx           # 钱包信息组件
│   ├── layout/                      # 布局组件目录
│   │   └── BaseLayout.jsx           # 基础布局组件
│   ├── network/                     # 网络相关组件
│   │   ├── NetworkSelector.jsx      # 网络选择器
│   │   └── RpcTester.jsx            # RPC连接测试器
│   └── wallet/                      # 钱包相关组件
│       └── WalletPreview.jsx        # 钱包预览组件
├── hooks/                           # 自定义Hook目录 (替代composables)
│   ├── useFileHandler.js            # 文件处理Hook
│   ├── useStorage.js                # 数据持久化Hook
│   ├── useWalletGeneration.js       # 钱包生成Hook
│   └── useWeb3.js                   # Web3交互Hook
├── utils/                           # 工具函数目录 (保持不变)
│   ├── constants.js                 # 常量配置
│   ├── formatters.js                # 格式化工具
│   ├── helpers.js                   # 辅助函数
│   └── validators.js                # 验证工具
├── styles/                          # 样式文件目录
│   ├── globals.css                  # 全局样式 (合并base.css + main.css)
│   └── components.css               # 组件样式 (shared.css)
└── lib/                             # Next.js特有的库文件
    └── web3-config.js               # Web3配置文件
```

## 4. 代码迁移指南

### 4.1 Vue 组件到 React 组件

#### Vue 组件示例 (WalletGenerator.vue)
```vue
<script setup>
import { ref, computed } from 'vue'
import { useWalletGeneration } from '@/composables/useWalletGeneration'

const numWallets = ref(1)
const useMnemonic = ref(true)
const generatedData = ref({ addresses: [], privateKeys: [], mnemonic: '' })

const { isGenerating, generateWallets } = useWalletGeneration()

const statsItems = computed(() => [
  {
    label: '生成钱包数量',
    value: generatedData.value.addresses.length.toString(),
    status: 'success'
  }
])

async function handleGenerateWallets() {
  const result = await generateWallets({
    useMnemonic: useMnemonic.value,
    numWallets: numWallets.value
  })
  if (result) {
    generatedData.value = result
  }
}
</script>

<template>
  <div class="wallet-generator">
    <input v-model="numWallets" type="number" />
    <button @click="handleGenerateWallets" :disabled="isGenerating">
      {{ isGenerating ? '生成中...' : '生成钱包' }}
    </button>
    <div v-if="generatedData.addresses.length > 0">
      生成了 {{ statsItems[0].value }} 个钱包
    </div>
  </div>
</template>
```

#### Next.js 组件示例 (WalletGenerator.jsx)
```jsx
'use client'
import { useState, useMemo } from 'react'
import { useWalletGeneration } from '@/hooks/useWalletGeneration'

export default function WalletGenerator() {
  const [numWallets, setNumWallets] = useState(1)
  const [useMnemonic, setUseMnemonic] = useState(true)
  const [generatedData, setGeneratedData] = useState({ 
    addresses: [], 
    privateKeys: [], 
    mnemonic: '' 
  })

  const { isGenerating, generateWallets } = useWalletGeneration()

  const statsItems = useMemo(() => [
    {
      label: '生成钱包数量',
      value: generatedData.addresses.length.toString(),
      status: 'success'
    }
  ], [generatedData.addresses.length])

  const handleGenerateWallets = async () => {
    const result = await generateWallets({
      useMnemonic,
      numWallets
    })
    if (result) {
      setGeneratedData(result)
    }
  }

  return (
    <div className="wallet-generator">
      <input 
        value={numWallets} 
        onChange={(e) => setNumWallets(Number(e.target.value))} 
        type="number" 
      />
      <button onClick={handleGenerateWallets} disabled={isGenerating}>
        {isGenerating ? '生成中...' : '生成钱包'}
      </button>
      {generatedData.addresses.length > 0 && (
        <div>
          生成了 {statsItems[0].value} 个钱包
        </div>
      )}
    </div>
  )
}
```

### 4.2 Composables 到 Custom Hooks

#### Vue Composable 示例 (useWalletGeneration.js)
```javascript
import { ref } from 'vue'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const isGenerating = ref(false)
  const currentWallet = ref(0)

  async function generateWallets(options) {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating.value) return null
    
    isGenerating.value = true
    currentWallet.value = 0
    
    try {
      // 生成逻辑...
      return result
    } finally {
      isGenerating.value = false
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

#### Next.js Custom Hook 示例 (useWalletGeneration.js)
```javascript
import { useState } from 'react'
import { ethers } from 'ethers'

export function useWalletGeneration() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentWallet, setCurrentWallet] = useState(0)

  const generateWallets = async (options) => {
    const { useMnemonic, numWallets } = options
    
    if (isGenerating) return null
    
    setIsGenerating(true)
    setCurrentWallet(0)
    
    try {
      // 生成逻辑...
      return result
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    isGenerating,
    currentWallet,
    generateWallets
  }
}
```

### 4.3 Tab 导航到路由导航

#### Vue Tab 导航 (App.vue)
```vue
<script setup>
import { ref } from 'vue'

const activeTab = ref('wallet')

function switchTab(tab) {
  activeTab.value = tab
}
</script>

<template>
  <div class="tab-nav">
    <button
      @click="switchTab('wallet')"
      :class="['tab-btn', { active: activeTab === 'wallet' }]"
    >
      🔐 生成钱包
    </button>
    <button
      @click="switchTab('disperse')"
      :class="['tab-btn', { active: activeTab === 'disperse' }]"
    >
      💸 批量分发
    </button>
  </div>

  <div class="tab-content">
    <WalletGenerator v-if="activeTab === 'wallet'" />
    <BatchDisperse v-if="activeTab === 'disperse'" />
  </div>
</template>
```

#### Next.js 路由导航 (layout.js)
```jsx
'use client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function RootLayout({ children }) {
  const pathname = usePathname()

  return (
    <html lang="zh">
      <body>
        <div className="tab-nav">
          <Link
            href="/"
            className={`tab-btn ${pathname === '/' ? 'active' : ''}`}
          >
            🔐 生成钱包
          </Link>
          <Link
            href="/disperse"
            className={`tab-btn ${pathname === '/disperse' ? 'active' : ''}`}
          >
            💸 批量分发
          </Link>
        </div>

        <div className="tab-content">
          {children}
        </div>
      </body>
    </html>
  )
}
```

### 4.4 数据持久化迁移

#### Vue useStorage Composable
```javascript
import { ref, watch } from 'vue'

export function useStorage(key, data, defaultValues) {
  // 从localStorage加载数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem(key)
      if (stored) {
        const parsed = JSON.parse(stored)
        Object.keys(parsed).forEach(k => {
          if (data[k] && data[k].value !== undefined) {
            data[k].value = parsed[k]
          }
        })
      }
    } catch (error) {
      console.error('加载存储数据失败:', error)
    }
  }

  // 保存到localStorage
  const saveToStorage = () => {
    try {
      const toSave = {}
      Object.keys(data).forEach(k => {
        toSave[k] = data[k].value
      })
      localStorage.setItem(key, JSON.stringify(toSave))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 监听数据变化并自动保存
  Object.keys(data).forEach(k => {
    watch(data[k], saveToStorage, { deep: true })
  })

  loadFromStorage()

  return { saveToStorage, loadFromStorage }
}
```

#### Next.js useStorage Hook
```javascript
import { useState, useEffect } from 'react'

export function useStorage(key, initialData, defaultValues) {
  const [data, setData] = useState(initialData)

  // 从localStorage加载数据
  useEffect(() => {
    try {
      const stored = localStorage.getItem(key)
      if (stored) {
        const parsed = JSON.parse(stored)
        setData(prevData => ({ ...prevData, ...parsed }))
      }
    } catch (error) {
      console.error('加载存储数据失败:', error)
    }
  }, [key])

  // 保存到localStorage
  useEffect(() => {
    try {
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }, [key, data])

  const updateData = (updates) => {
    setData(prevData => ({ ...prevData, ...updates }))
  }

  const clearStoredData = () => {
    localStorage.removeItem(key)
    setData(defaultValues)
  }

  return { data, updateData, clearStoredData }
}
```

## 5. 配置与依赖迁移

### 5.1 构建配置对比

#### Vite 配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

export default defineConfig({
  plugins: [
    vue(),
    nodePolyfills({
      protocolImports: true,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['buffer', 'ethers']
  }
})
```

#### Next.js 配置 (next.config.js)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        buffer: require.resolve('buffer'),
      }
    }

    config.plugins.push(
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser',
      })
    )

    return config
  },
}

module.exports = nextConfig
```

### 5.2 依赖包迁移

#### Vue 项目依赖 (package.json)
```json
{
  "dependencies": {
    "@ethereumjs/wallet": "^10.0.0",
    "bip39": "^3.1.0",
    "buffer": "^6.0.3",
    "ethereumjs-wallet": "^1.0.2",
    "ethers": "^6.15.0",
    "vue": "^3.5.18",
    "web3": "^4.16.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^6.0.1",
    "vite": "^7.0.6",
    "vite-plugin-node-polyfills": "^0.24.0"
  }
}
```

#### Next.js 项目依赖 (package.json)
```json
{
  "dependencies": {
    "@ethereumjs/wallet": "^10.0.0",
    "bip39": "^3.1.0",
    "buffer": "^6.0.3",
    "crypto-browserify": "^3.12.0",
    "ethereumjs-wallet": "^1.0.2",
    "ethers": "^6.15.0",
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "stream-browserify": "^3.0.0",
    "web3": "^4.16.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "webpack": "^5.0.0"
  }
}
```

## 6. 环境变量与构建

### 6.1 环境变量配置

#### Vue 项目 (.env)
```bash
# Vue 环境变量以 VITE_ 开头
VITE_APP_TITLE=AK47Tool
VITE_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
VITE_DISPERSE_CONTRACT=0x123...
```

#### Next.js 项目 (.env.local)
```bash
# Next.js 环境变量以 NEXT_PUBLIC_ 开头（客户端可访问）
NEXT_PUBLIC_APP_TITLE=AK47Tool
NEXT_PUBLIC_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
NEXT_PUBLIC_DISPERSE_CONTRACT=0x123...

# 服务端环境变量（不需要前缀）
DATABASE_URL=postgresql://...
API_SECRET_KEY=your-secret-key
```

### 6.2 构建脚本对比

#### Vue 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

#### Next.js 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

## 7. 典型功能示例

### 7.1 完整组件迁移示例

#### Vue 组件 (BatchDisperse.vue 片段)
```vue
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useWeb3 } from '@/composables/useWeb3'
import { useStorage } from '@/composables/useStorage'

const privateKey = ref('')
const addressData = ref([])
const isRunning = ref(false)
const executedCount = ref(0)

const { web3, initWeb3, testRpcConnection } = useWeb3()

const totalAmount = computed(() => {
  return addressData.value.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
})

onMounted(() => {
  initWeb3('https://mainnet.infura.io/v3/your-key')
})

async function startDisperse() {
  if (!privateKey.value.trim()) {
    alert('请输入私钥')
    return
  }

  isRunning.value = true
  executedCount.value = 0

  try {
    for (let i = 0; i < addressData.value.length; i++) {
      const item = addressData.value[i]
      await sendTransaction(item.address, item.amount)
      executedCount.value++
    }
  } catch (error) {
    console.error('分发失败:', error)
  } finally {
    isRunning.value = false
  }
}

async function sendTransaction(to, amount) {
  // 发送交易逻辑
}
</script>

<template>
  <div class="batch-disperse">
    <div class="form-group">
      <label>私钥</label>
      <input v-model="privateKey" type="password" placeholder="输入私钥" />
    </div>

    <div class="stats">
      <p>总金额: {{ totalAmount }} ETH</p>
      <p>已执行: {{ executedCount }}/{{ addressData.length }}</p>
    </div>

    <button @click="startDisperse" :disabled="isRunning">
      {{ isRunning ? '执行中...' : '开始分发' }}
    </button>
  </div>
</template>
```

#### Next.js 组件 (BatchDisperse.jsx)
```jsx
'use client'
import { useState, useMemo, useEffect } from 'react'
import { useWeb3 } from '@/hooks/useWeb3'
import { useStorage } from '@/hooks/useStorage'

export default function BatchDisperse() {
  const [privateKey, setPrivateKey] = useState('')
  const [addressData, setAddressData] = useState([])
  const [isRunning, setIsRunning] = useState(false)
  const [executedCount, setExecutedCount] = useState(0)

  const { web3, initWeb3, testRpcConnection } = useWeb3()

  const totalAmount = useMemo(() => {
    return addressData.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
  }, [addressData])

  useEffect(() => {
    initWeb3('https://mainnet.infura.io/v3/your-key')
  }, [])

  const startDisperse = async () => {
    if (!privateKey.trim()) {
      alert('请输入私钥')
      return
    }

    setIsRunning(true)
    setExecutedCount(0)

    try {
      for (let i = 0; i < addressData.length; i++) {
        const item = addressData[i]
        await sendTransaction(item.address, item.amount)
        setExecutedCount(i + 1)
      }
    } catch (error) {
      console.error('分发失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const sendTransaction = async (to, amount) => {
    // 发送交易逻辑
  }

  return (
    <div className="batch-disperse">
      <div className="form-group">
        <label>私钥</label>
        <input
          value={privateKey}
          onChange={(e) => setPrivateKey(e.target.value)}
          type="password"
          placeholder="输入私钥"
        />
      </div>

      <div className="stats">
        <p>总金额: {totalAmount} ETH</p>
        <p>已执行: {executedCount}/{addressData.length}</p>
      </div>

      <button onClick={startDisperse} disabled={isRunning}>
        {isRunning ? '执行中...' : '开始分发'}
      </button>
    </div>
  )
}
```

### 7.2 API 调用示例

#### Vue 中的 Web3 调用
```javascript
// composables/useWeb3.js
import { ref } from 'vue'
import { ethers } from 'ethers'

export function useWeb3() {
  const web3 = ref(null)
  const provider = ref(null)

  const initWeb3 = (rpcUrl) => {
    try {
      provider.value = new ethers.JsonRpcProvider(rpcUrl)
      web3.value = provider.value
    } catch (error) {
      console.error('Web3初始化失败:', error)
    }
  }

  const getBalance = async (address) => {
    if (!provider.value) return '0'

    try {
      const balance = await provider.value.getBalance(address)
      return ethers.formatEther(balance)
    } catch (error) {
      console.error('获取余额失败:', error)
      return '0'
    }
  }

  return {
    web3,
    provider,
    initWeb3,
    getBalance
  }
}
```

#### Next.js 中的 Web3 调用
```javascript
// hooks/useWeb3.js
import { useState, useCallback } from 'react'
import { ethers } from 'ethers'

export function useWeb3() {
  const [web3, setWeb3] = useState(null)
  const [provider, setProvider] = useState(null)

  const initWeb3 = useCallback((rpcUrl) => {
    try {
      const newProvider = new ethers.JsonRpcProvider(rpcUrl)
      setProvider(newProvider)
      setWeb3(newProvider)
    } catch (error) {
      console.error('Web3初始化失败:', error)
    }
  }, [])

  const getBalance = useCallback(async (address) => {
    if (!provider) return '0'

    try {
      const balance = await provider.getBalance(address)
      return ethers.formatEther(balance)
    } catch (error) {
      console.error('获取余额失败:', error)
      return '0'
    }
  }, [provider])

  return {
    web3,
    provider,
    initWeb3,
    getBalance
  }
}
```

## 8. 常见问题与注意事项

### 8.1 响应式数据处理

**问题**: Vue 的 `ref()` 和 `reactive()` 在 React 中的对应处理

**解决方案**:
- Vue `ref()` → React `useState()`
- Vue `reactive()` → React `useState()` 配合对象
- Vue `computed()` → React `useMemo()`
- Vue `watch()` → React `useEffect()`

### 8.2 生命周期差异

**问题**: Vue 生命周期钩子在 React 中的对应

**解决方案**:
```javascript
// Vue
onMounted(() => {
  // 组件挂载后执行
})

onUnmounted(() => {
  // 组件卸载前执行
})

// React
useEffect(() => {
  // 组件挂载后执行

  return () => {
    // 组件卸载前执行
  }
}, [])
```

### 8.3 事件处理差异

**问题**: Vue 的事件修饰符在 React 中的处理

**解决方案**:
```javascript
// Vue
<button @click.prevent="handleClick">点击</button>

// React
<button onClick={(e) => {
  e.preventDefault()
  handleClick()
}}>点击</button>
```

### 8.4 样式绑定差异

**问题**: Vue 的动态样式绑定在 React 中的处理

**解决方案**:
```javascript
// Vue
<div :class="['base-class', { active: isActive }]">

// React
<div className={`base-class ${isActive ? 'active' : ''}`}>
```

### 8.5 Web3 兼容性问题

**问题**: Node.js polyfills 在浏览器环境中的处理

**解决方案**:
1. 安装必要的 polyfills
2. 配置 webpack fallbacks
3. 使用 ProvidePlugin 提供全局变量

### 8.6 状态管理复杂度

**问题**: Vue Composables 的全局状态在 React 中的处理

**解决方案**:
- 简单状态：使用 Context API
- 复杂状态：使用 Zustand 或 Redux Toolkit
- 服务端状态：使用 TanStack Query

## 9. 迁移 Checklist

### 9.1 项目初始化
- [ ] 创建 Next.js 项目 (`npx create-next-app@latest`)
- [ ] 配置 TypeScript (可选)
- [ ] 设置 ESLint 和 Prettier
- [ ] 配置 `next.config.js` 文件
- [ ] 安装必要的依赖包

### 9.2 目录结构迁移
- [ ] 创建 `src/app` 目录结构
- [ ] 迁移 `components` 目录
- [ ] 创建 `hooks` 目录 (替代 composables)
- [ ] 迁移 `utils` 目录
- [ ] 设置 `styles` 目录

### 9.3 组件迁移
#### 主要组件
- [ ] 迁移 `WalletGenerator` 组件
  - [ ] 转换 `<script setup>` 到 React function component
  - [ ] 迁移 `ref()` 到 `useState()`
  - [ ] 迁移 `computed()` 到 `useMemo()`
  - [ ] 迁移事件处理函数
  - [ ] 更新模板语法到JSX
- [ ] 迁移 `BatchDisperse` 组件
  - [ ] 转换双模式分发逻辑
  - [ ] 迁移Gas价格监控功能
  - [ ] 迁移文件导入处理
  - [ ] 迁移执行控制逻辑
- [ ] 迁移 `ContractInteraction` 组件
  - [ ] 转换三种交互模式
  - [ ] 迁移钱包管理功能
  - [ ] 迁移代币归集逻辑
  - [ ] 迁移循环交互功能

#### 通用组件 (common/)
- [ ] 迁移 `BaseButton.vue` → `BaseButton.jsx`
  - [ ] 转换props验证
  - [ ] 迁移variant和size逻辑
  - [ ] 迁移loading状态处理
- [ ] 迁移 `BaseCard.vue` → `BaseCard.jsx`
  - [ ] 转换slot到children
  - [ ] 迁移header和actions插槽
- [ ] 迁移 `FormInput.vue` → `FormInput.jsx`
  - [ ] 转换v-model到value+onChange
  - [ ] 迁移输入验证逻辑
- [ ] 迁移 `ProgressBar.vue` → `ProgressBar.jsx`
  - [ ] 迁移进度计算逻辑
  - [ ] 转换动态样式绑定
- [ ] 迁移 `StatusGrid.vue` → `StatusGrid.jsx`
  - [ ] 转换v-for到map()
  - [ ] 迁移状态显示逻辑
- [ ] 迁移 `AddressDisplay.vue` → `AddressDisplay.jsx`
  - [ ] 迁移地址截断逻辑
  - [ ] 迁移复制功能
- [ ] 迁移 `AddressPreview.vue` → `AddressPreview.jsx`
  - [ ] 迁移批量地址显示
  - [ ] 迁移总金额计算
- [ ] 迁移 `DataInput.vue` → `DataInput.jsx`
  - [ ] 迁移文件上传和文本输入
  - [ ] 迁移数据解析逻辑
- [ ] 迁移 `FileUpload.vue` → `FileUpload.jsx`
  - [ ] 迁移拖拽上传功能
  - [ ] 迁移文件类型验证
- [ ] 迁移其他通用组件
  - [ ] `ActionButtonGroup.vue` → `ActionButtonGroup.jsx`
  - [ ] `FormatHelp.vue` → `FormatHelp.jsx`
  - [ ] `FormatItem.vue` → `FormatItem.jsx`
  - [ ] `TransactionResults.vue` → `TransactionResults.jsx`
  - [ ] `WalletInfo.vue` → `WalletInfo.jsx`

#### 布局组件 (layout/)
- [ ] 迁移 `BaseLayout.vue` → `BaseLayout.jsx`
  - [ ] 转换slot系统到children
  - [ ] 迁移响应式布局逻辑
  - [ ] 迁移单栏/双栏/三栏布局

#### 网络组件 (network/)
- [ ] 迁移 `NetworkSelector.vue` → `NetworkSelector.jsx`
  - [ ] 转换v-model到受控组件
  - [ ] 迁移网络切换逻辑
- [ ] 迁移 `RpcTester.vue` → `RpcTester.jsx`
  - [ ] 迁移RPC连接测试
  - [ ] 迁移状态显示逻辑

#### 钱包组件 (wallet/)
- [ ] 迁移 `WalletPreview.vue` → `WalletPreview.jsx`
  - [ ] 迁移钱包列表显示
  - [ ] 迁移余额查询功能

### 9.4 功能迁移 (Composables → Custom Hooks)
- [ ] 迁移 `useWalletGeneration` hook
  - [ ] 转换 `ref()` 到 `useState()`
  - [ ] 迁移助记词生成逻辑
  - [ ] 迁移随机钱包生成逻辑
  - [ ] 迁移进度跟踪功能
  - [ ] 迁移私钥验证功能
  - [ ] 测试钱包生成功能
- [ ] 迁移 `useWeb3` hook
  - [ ] 转换响应式变量到state
  - [ ] 迁移RPC连接管理
  - [ ] 迁移网络信息获取
  - [ ] 迁移余额查询功能
  - [ ] 迁移交易发送功能
  - [ ] 迁移合约交互功能
  - [ ] 迁移Gas价格监控
  - [ ] 测试所有Web3功能
- [ ] 迁移 `useStorage` hook
  - [ ] 转换Vue的watch到useEffect
  - [ ] 迁移localStorage读写逻辑
  - [ ] 迁移数据自动保存功能
  - [ ] 迁移数据恢复功能
  - [ ] 测试数据持久化
- [ ] 迁移 `useFileHandler` hook
  - [ ] 迁移文件上传处理
  - [ ] 迁移文本解析逻辑
  - [ ] 迁移文件下载功能
  - [ ] 迁移数据验证功能
  - [ ] 测试文件处理功能
- [ ] 验证所有hooks的功能完整性
  - [ ] 单元测试编写
  - [ ] 集成测试验证
  - [ ] 性能测试对比

### 9.5 路由设置
- [ ] 创建根布局 (`app/layout.js`)
- [ ] 创建首页 (`app/page.js`)
- [ ] 创建分发页面 (`app/disperse/page.js`)
- [ ] 创建合约交互页面 (`app/contract/page.js`)
- [ ] 实现导航组件

### 9.6 样式迁移
- [ ] 迁移全局样式到 `globals.css`
- [ ] 更新 CSS 类名语法 (className)
- [ ] 测试响应式设计
- [ ] 验证主题和颜色

### 9.7 配置迁移
- [ ] 设置环境变量 (`.env.local`)
- [ ] 配置 webpack polyfills
- [ ] 测试 Web3 库兼容性
- [ ] 配置构建脚本

### 9.8 测试验证
#### 功能测试
- [ ] 测试钱包生成功能
  - [ ] 助记词模式生成测试
  - [ ] 随机模式生成测试
  - [ ] 批量生成性能测试
  - [ ] 数据导出功能测试
  - [ ] 进度显示准确性测试
- [ ] 测试批量分发功能
  - [ ] 合约分发模式测试
  - [ ] 私钥逐笔分发测试
  - [ ] Gas价格监控测试
  - [ ] 文件导入解析测试
  - [ ] 余额检查功能测试
  - [ ] 执行控制功能测试
- [ ] 测试合约交互功能
  - [ ] ETH归集功能测试
  - [ ] 代币归集功能测试
  - [ ] 循环交互功能测试
  - [ ] 钱包管理功能测试
  - [ ] 余额查询功能测试
  - [ ] 交易重试机制测试
- [ ] 测试数据持久化
  - [ ] 数据自动保存测试
  - [ ] 页面刷新恢复测试
  - [ ] 数据清除功能测试
  - [ ] 跨组件数据共享测试

#### 界面测试
- [ ] 测试响应式布局
  - [ ] 桌面端布局测试
  - [ ] 平板端布局测试
  - [ ] 移动端布局测试
  - [ ] 组件自适应测试
- [ ] 测试用户交互
  - [ ] 按钮点击响应测试
  - [ ] 表单输入验证测试
  - [ ] 文件拖拽上传测试
  - [ ] 复制粘贴功能测试

#### 性能测试
- [ ] 测试生产构建
  - [ ] 构建成功验证
  - [ ] 打包体积检查
  - [ ] 加载性能测试
  - [ ] 运行时性能测试

#### 兼容性测试
- [ ] 浏览器兼容性测试
  - [ ] Chrome浏览器测试
  - [ ] Firefox浏览器测试
  - [ ] Safari浏览器测试
  - [ ] Edge浏览器测试
- [ ] 网络兼容性测试
  - [ ] 以太坊主网测试
  - [ ] BSC网络测试
  - [ ] Polygon网络测试
  - [ ] 自定义RPC测试

### 9.9 部署准备
- [ ] 优化构建配置
- [ ] 设置生产环境变量
- [ ] 测试静态导出 (如需要)
- [ ] 准备部署脚本

### 9.10 文档更新
- [ ] 更新 README.md
- [ ] 更新开发文档
- [ ] 记录迁移过程中的问题和解决方案
- [ ] 准备团队培训材料

---

## 总结

本文档提供了从 Vue.js 到 Next.js 的完整迁移指南，涵盖了项目的所有关键方面。迁移过程中需要特别注意：

1. **响应式数据处理**：从 Vue 的 ref/reactive 转换为 React 的 useState
2. **组件生命周期**：从 Vue 的生命周期钩子转换为 React 的 useEffect
3. **路由系统**：从单页面 Tab 切换转换为 Next.js 的文件系统路由
4. **状态管理**：从 Vue Composables 转换为 React Custom Hooks
5. **Web3 兼容性**：确保 Node.js polyfills 在浏览器环境中正常工作

按照本文档的步骤和 Checklist 进行迁移，可以确保功能的完整性和代码质量。建议分阶段进行迁移，每完成一个模块就进行充分测试，确保迁移的稳定性。

## 10. 详细功能对照表

### 10.1 主要功能对照

| 功能模块 | Vue实现 | Next.js实现 | 迁移重点 |
|---------|---------|-------------|----------|
| 钱包生成 | WalletGenerator.vue | WalletGenerator.jsx | ref→useState, computed→useMemo |
| 批量分发 | BatchDisperse.vue | BatchDisperse.jsx | 双模式逻辑, Gas监控 |
| 合约交互 | ContractInteraction.vue | ContractInteraction.jsx | 三种模式, 交易重试 |
| 数据持久化 | useStorage composable | useStorage hook | watch→useEffect |
| Web3交互 | useWeb3 composable | useWeb3 hook | 响应式→state管理 |
| 文件处理 | useFileHandler composable | useFileHandler hook | 文件API保持一致 |
| 钱包生成逻辑 | useWalletGeneration composable | useWalletGeneration hook | ethers.js逻辑不变 |

### 10.2 组件功能对照

| 组件类型 | Vue组件 | React组件 | 主要Props | 主要Events | 迁移注意点 |
|---------|---------|-----------|-----------|------------|------------|
| 按钮 | BaseButton.vue | BaseButton.jsx | variant, size, loading, disabled | onClick | loading状态处理 |
| 卡片 | BaseCard.vue | BaseCard.jsx | title, variant | - | slot→children |
| 输入框 | FormInput.vue | FormInput.jsx | label, type, value | onChange | v-model→受控组件 |
| 进度条 | ProgressBar.vue | ProgressBar.jsx | current, total, message | - | 动态样式计算 |
| 状态网格 | StatusGrid.vue | StatusGrid.jsx | items | - | v-for→map() |
| 地址显示 | AddressDisplay.vue | AddressDisplay.jsx | address, maxLength | onCopy | 复制功能 |
| 文件上传 | FileUpload.vue | FileUpload.jsx | acceptedTypes | onFileSelected | 拖拽处理 |
| 网络选择 | NetworkSelector.vue | NetworkSelector.jsx | modelValue, defaultNetwork | onNetworkChange | 双向绑定 |
| RPC测试 | RpcTester.vue | RpcTester.jsx | rpcUrl | onConnectionStatus | 异步状态管理 |

### 10.3 工具函数对照

| 工具类型 | 函数名 | Vue使用 | React使用 | 迁移状态 |
|---------|--------|---------|-----------|----------|
| 格式化 | formatAddress | 直接调用 | 直接调用 | 无需修改 |
| 格式化 | formatEthAmount | 直接调用 | 直接调用 | 无需修改 |
| 格式化 | formatGasPrice | 直接调用 | 直接调用 | 无需修改 |
| 验证 | isValidAddress | 直接调用 | 直接调用 | 无需修改 |
| 验证 | isValidPrivateKey | 直接调用 | 直接调用 | 无需修改 |
| 辅助 | copyToClipboard | 直接调用 | 直接调用 | 无需修改 |
| 辅助 | sleep | 直接调用 | 直接调用 | 无需修改 |
| 常量 | NETWORKS | 导入使用 | 导入使用 | 无需修改 |
| 常量 | DEFAULT_VALUES | 导入使用 | 导入使用 | 无需修改 |

### 10.4 样式迁移对照

| 样式类型 | Vue实现 | Next.js实现 | 迁移方式 |
|---------|---------|-------------|----------|
| 全局样式 | base.css + main.css | globals.css | 合并文件 |
| 组件样式 | shared.css | components.css | 重命名 |
| 动态类名 | :class="[...]" | className={...} | 语法转换 |
| 条件样式 | :class="{ active: isActive }" | className={isActive ? 'active' : ''} | 三元表达式 |
| 内联样式 | :style="{ color: textColor }" | style={{ color: textColor }} | 对象语法 |
| CSS变量 | var(--primary-color) | var(--primary-color) | 保持不变 |

这个详细的功能对照表确保了迁移过程中不会遗漏任何功能细节，每个组件、函数、样式都有明确的迁移路径和注意事项。

## 6. 环境变量与构建

### 6.1 环境变量配置

#### Vue 项目 (.env)
```bash
# Vue 环境变量以 VITE_ 开头
VITE_APP_TITLE=AK47Tool
VITE_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
VITE_DISPERSE_CONTRACT=0x123...
```

#### Next.js 项目 (.env.local)
```bash
# Next.js 环境变量以 NEXT_PUBLIC_ 开头（客户端可访问）
NEXT_PUBLIC_APP_TITLE=AK47Tool
NEXT_PUBLIC_DEFAULT_RPC_URL=https://mainnet.infura.io/v3/your-key
NEXT_PUBLIC_DISPERSE_CONTRACT=0x123...

# 服务端环境变量（不需要前缀）
DATABASE_URL=postgresql://...
API_SECRET_KEY=your-secret-key
```

### 6.2 构建脚本对比

#### Vue 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

#### Next.js 项目 (package.json scripts)
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

## 7. 典型功能示例

### 7.1 完整组件迁移示例

#### Vue 组件 (BatchDisperse.vue 片段)
```vue
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useWeb3 } from '@/composables/useWeb3'
import { useStorage } from '@/composables/useStorage'

const privateKey = ref('')
const addressData = ref([])
const isRunning = ref(false)
const executedCount = ref(0)

const { web3, initWeb3, testRpcConnection } = useWeb3()

const totalAmount = computed(() => {
  return addressData.value.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
})

onMounted(() => {
  initWeb3('https://mainnet.infura.io/v3/your-key')
})

async function startDisperse() {
  if (!privateKey.value.trim()) {
    alert('请输入私钥')
    return
  }

  isRunning.value = true
  executedCount.value = 0

  try {
    for (let i = 0; i < addressData.value.length; i++) {
      const item = addressData.value[i]
      await sendTransaction(item.address, item.amount)
      executedCount.value++
    }
  } catch (error) {
    console.error('分发失败:', error)
  } finally {
    isRunning.value = false
  }
}

async function sendTransaction(to, amount) {
  // 发送交易逻辑
}
</script>

<template>
  <div class="batch-disperse">
    <div class="form-group">
      <label>私钥</label>
      <input v-model="privateKey" type="password" placeholder="输入私钥" />
    </div>

    <div class="stats">
      <p>总金额: {{ totalAmount }} ETH</p>
      <p>已执行: {{ executedCount }}/{{ addressData.length }}</p>
    </div>

    <button @click="startDisperse" :disabled="isRunning">
      {{ isRunning ? '执行中...' : '开始分发' }}
    </button>
  </div>
</template>
```

#### Next.js 组件 (BatchDisperse.jsx)
```jsx
'use client'
import { useState, useMemo, useEffect } from 'react'
import { useWeb3 } from '@/hooks/useWeb3'
import { useStorage } from '@/hooks/useStorage'

export default function BatchDisperse() {
  const [privateKey, setPrivateKey] = useState('')
  const [addressData, setAddressData] = useState([])
  const [isRunning, setIsRunning] = useState(false)
  const [executedCount, setExecutedCount] = useState(0)

  const { web3, initWeb3, testRpcConnection } = useWeb3()

  const totalAmount = useMemo(() => {
    return addressData.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
  }, [addressData])

  useEffect(() => {
    initWeb3('https://mainnet.infura.io/v3/your-key')
  }, [])

  const startDisperse = async () => {
    if (!privateKey.trim()) {
      alert('请输入私钥')
      return
    }

    setIsRunning(true)
    setExecutedCount(0)

    try {
      for (let i = 0; i < addressData.length; i++) {
        const item = addressData[i]
        await sendTransaction(item.address, item.amount)
        setExecutedCount(i + 1)
      }
    } catch (error) {
      console.error('分发失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const sendTransaction = async (to, amount) => {
    // 发送交易逻辑
  }

  return (
    <div className="batch-disperse">
      <div className="form-group">
        <label>私钥</label>
        <input
          value={privateKey}
          onChange={(e) => setPrivateKey(e.target.value)}
          type="password"
          placeholder="输入私钥"
        />
      </div>

      <div className="stats">
        <p>总金额: {totalAmount} ETH</p>
        <p>已执行: {executedCount}/{addressData.length}</p>
      </div>

      <button onClick={startDisperse} disabled={isRunning}>
        {isRunning ? '执行中...' : '开始分发'}
      </button>
    </div>
  )
}
```

/**
 * 格式化地址显示 (响应式显示)
 * @param {string} address - 钱包地址
 * @param {number} maxLength - 最大显示长度
 * @returns {string} 格式化后的地址
 */
export function formatAddress(address, maxLength = 42) {
  if (!address) return ''
  
  // 如果地址长度小于等于最大长度，直接返回
  if (address.length <= maxLength) {
    return address
  }
  
  // 根据maxLength决定显示格式
  if (maxLength <= 15) {
    // 极短格式：0x1234...abcd (4+4)
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  } else if (maxLength <= 20) {
    // 短格式：0x1234...abcdef (6+6)
    return `${address.slice(0, 6)}...${address.slice(-6)}`
  } else if (maxLength <= 30) {
    // 中等格式：0x123456...abcdef78 (8+8)
    return `${address.slice(0, 8)}...${address.slice(-8)}`
  } else {
    // 长格式：0x12345678...abcdef7890 (10+10)
    return `${address.slice(0, 10)}...${address.slice(-10)}`
  }
}

/**
 * 格式化ETH数量显示
 * @param {number|string} amount - ETH数量
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的数量
 */
export function formatEthAmount(amount, decimals = 4) {
  if (amount === undefined || amount === null) return '0'
  const num = parseFloat(amount)
  if (isNaN(num)) return '0'
  
  // 先用 toFixed 格式化，然后去掉尾部的0
  const fixed = num.toFixed(decimals)
  // 使用 parseFloat 去掉尾部的0，再转回字符串
  return parseFloat(fixed).toString()
}

/**
 * 格式化Gas价格显示
 * @param {number} gasPrice - Gas价格（Gwei）
 * @returns {string} 格式化后的Gas价格
 */
export function formatGasPrice(gasPrice) {
  if (gasPrice === null || gasPrice === undefined || gasPrice === 0 || isNaN(gasPrice)) {
    return '未获取'
  }
  return `${parseFloat(gasPrice.toFixed(4))} Gwei`
}

/**
 * 格式化交易哈希显示
 * @param {string} txHash - 交易哈希
 * @param {number} maxLength - 最大显示长度
 * @returns {string} 格式化后的交易哈希
 */
export function formatTxHash(txHash, maxLength = 20) {
  if (!txHash) return ''
  if (txHash.length <= maxLength) return txHash
  
  const prefixLength = Math.floor((maxLength - 3) / 2)
  const suffixLength = maxLength - 3 - prefixLength
  return `${txHash.slice(0, prefixLength)}...${txHash.slice(-suffixLength)}`
}

/**
 * 格式化文件大小显示
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化时间显示
 * @param {Date|string|number} date - 时间
 * @returns {string} 格式化后的时间
 */
export function formatTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化数字显示（添加千分位分隔符）
 * @param {number|string} num - 数字
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num) {
  if (num === undefined || num === null) return '0'
  const number = parseFloat(num)
  if (isNaN(number)) return '0'
  return number.toLocaleString('zh-CN')
} 
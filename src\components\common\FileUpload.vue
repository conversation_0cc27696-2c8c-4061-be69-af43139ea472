<template>
  <div class="file-upload-section">
    <div 
      class="upload-area" 
      @click="triggerFileUpload" 
      @dragover.prevent 
      @drop.prevent="handleFileDrop"
    >
      <input 
        ref="fileInput" 
        type="file" 
        :accept="acceptedTypes" 
        @change="handleFileUpload" 
        style="display: none"
      />
      <div class="upload-content">
        <span class="upload-icon">📁</span>
        <p>{{ uploadText }}</p>
        <small v-if="description">{{ description }}</small>
      </div>
    </div>
    
    <div v-if="error" class="error-message">
      <span class="error-icon">❌</span>
      <span class="error-text">{{ error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  acceptedTypes: {
    type: String,
    default: '.txt'
  },
  uploadText: {
    type: String,
    default: '点击选择或拖拽文件到这里'
  },
  description: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 10 // MB
  }
})

const emit = defineEmits(['file-selected', 'error'])

const fileInput = ref(null)
const error = ref('')

function triggerFileUpload() {
  fileInput.value?.click()
}

function handleFileDrop(event) {
  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

function handleFileUpload(event) {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

function processFile(file) {
  error.value = ''
  
  // 验证文件类型
  if (props.acceptedTypes && !isValidFileType(file)) {
    error.value = `不支持的文件类型。支持的类型: ${props.acceptedTypes}`
    emit('error', error.value)
    return
  }
  
  // 验证文件大小
  if (!isValidFileSize(file)) {
    error.value = `文件过大。最大支持 ${props.maxSize}MB`
    emit('error', error.value)
    return
  }
  
  emit('file-selected', file)
}

function isValidFileType(file) {
  if (!props.acceptedTypes) return true
  
  const fileName = file.name.toLowerCase()
  const types = props.acceptedTypes.split(',').map(type => type.trim())
  return types.some(type => fileName.endsWith(type))
}

function isValidFileSize(file) {
  const maxSizeInBytes = props.maxSize * 1024 * 1024
  return file.size <= maxSizeInBytes
}
</script>

<style scoped>
.file-upload-section {
  margin-bottom: 20px;
}

.upload-area {
  border: 2px dashed #666;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(60, 60, 60, 0.3);
  color: #cccccc;
}

.upload-area:hover {
  border-color: #6b9bd1;
  background: rgba(80, 80, 80, 0.5);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-icon {
  font-size: 2em;
}

.error-message {
  background: rgba(80, 40, 40, 0.6);
  border: 1px solid rgba(245, 101, 101, 0.5);
  border-radius: 6px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e53e3e;
  font-weight: 500;
  font-size: 13px;
  margin-top: 10px;
}

.error-icon {
  font-size: 1.2em;
}

.error-text {
  flex: 1;
}
</style> 
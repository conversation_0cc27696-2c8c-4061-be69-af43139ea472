<template>
  <BaseCard v-if="address" title="💰 发送者信息">
    <template #actions>
      <BaseButton
        size="small"
        variant="info"
        :loading="isRefreshing"
        :loading-text="refreshingText"
        @click="$emit('refresh')"
      >
        🔄 刷新余额
      </BaseButton>
    </template>
    
    <div class="sender-info">
      <div class="info-item">
        <label>地址:</label>
        <AddressDisplay :address="address" :max-length="16" />
      </div>
      <div class="info-item">
        <label>余额:</label>
        <span class="balance" :class="{ 'insufficient': balance < requiredAmount }">
          {{ balance.toFixed(6) }} ETH
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import BaseCard from './BaseCard.vue'
import BaseButton from './BaseButton.vue'
import AddressDisplay from './AddressDisplay.vue'

defineProps({
  address: {
    type: String,
    default: ''
  },
  balance: {
    type: Number,
    default: 0
  },
  requiredAmount: {
    type: Number,
    default: 0
  },
  isRefreshing: {
    type: Boolean,
    default: false
  },
  refreshingText: {
    type: String,
    default: '刷新中...'
  }
})

defineEmits(['refresh'])
</script>

<style scoped>
.sender-info {
  background: rgba(107, 155, 209, 0.1);
  border: 1px solid rgba(107, 155, 209, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(120, 120, 120, 0.2);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  color: #cccccc;
  font-weight: 500;
  min-width: 80px;
}

.balance {
  color: #27ae60;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.balance.insufficient {
  color: #e53e3e;
}

@media (max-width: 768px) {
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
<template>
  <div class="data-input-section">
    <!-- 输入方式切换按钮 -->
    <div class="input-mode-toggle">
      <div class="toggle-buttons">
        <button 
          :class="['toggle-btn', { active: inputMode === 'file' }]"
          @click="setInputMode('file')"
        >
          📁 文件上传
        </button>
        <button 
          :class="['toggle-btn', { active: inputMode === 'text' }]"
          @click="setInputMode('text')"
        >
          ✏️ 直接输入
        </button>
      </div>
    </div>

    <!-- 文件上传模式 -->
    <div v-if="inputMode === 'file'" class="file-input-mode">
      <FileUpload 
        :accepted-types="acceptedTypes"
        :description="fileDescription"
        @file-selected="handleFileSelected"
        @error="handleFileError"
      />
    </div>

    <!-- 文本输入模式 -->
    <div v-if="inputMode === 'text'" class="text-input-mode">
      <div class="text-input-container">
        <label class="text-input-label">{{ textInputLabel }}</label>
        <textarea
          v-model="textContent"
          :placeholder="textPlaceholder"
          class="text-input-area"
          rows="10"
          @input="handleTextInput"
        />
        <div class="text-input-actions">
          <button 
            class="parse-btn"
            :disabled="!textContent.trim() || isParsing"
            @click="parseTextContent"
          >
            <span v-if="isParsing">⏳ 解析中...</span>
            <span v-else>🔄 解析内容</span>
          </button>
          <button 
            class="clear-btn"
            :disabled="!textContent.trim()"
            @click="clearTextContent"
          >
            🗑️ 清空
          </button>
        </div>
        <div v-if="textInputDescription" class="text-input-description">
          {{ textInputDescription }}
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="error" class="error-message">
      <span class="error-icon">❌</span>
      <span class="error-text">{{ error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from './FileUpload.vue'

const props = defineProps({
  acceptedTypes: {
    type: String,
    default: '.txt'
  },
  fileDescription: {
    type: String,
    default: '支持 .txt 文件格式'
  },
  textInputLabel: {
    type: String,
    default: '直接输入内容'
  },
  textPlaceholder: {
    type: String,
    default: '请输入内容，每行一条记录...'
  },
  textInputDescription: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['data-parsed', 'error'])

const inputMode = ref('file') // 'file' 或 'text'
const textContent = ref('')
const error = ref('')
const isParsing = ref(false)

function setInputMode(mode) {
  inputMode.value = mode
  error.value = ''
  
  // 切换模式时清空之前的错误
  if (mode === 'text') {
    // 切换到文本模式时，如果有内容就自动解析
    if (textContent.value.trim()) {
      setTimeout(() => parseTextContent(), 100)
    }
  }
}

function handleFileSelected(file) {
  emit('data-parsed', file, 'file')
}

function handleFileError(errorMessage) {
  error.value = errorMessage
  emit('error', errorMessage)
}

function handleTextInput() {
  error.value = ''
}

async function parseTextContent() {
  if (!textContent.value.trim()) {
    return
  }

  isParsing.value = true
  error.value = ''

  try {
    // 创建一个虚拟的文件对象来保持与现有解析逻辑的兼容性
    const blob = new Blob([textContent.value], { type: 'text/plain' })
    const virtualFile = new File([blob], 'input.txt', { type: 'text/plain' })
    
    emit('data-parsed', virtualFile, 'text')
  } catch (err) {
    error.value = '解析内容失败: ' + err.message
    emit('error', error.value)
  } finally {
    isParsing.value = false
  }
}

function clearTextContent() {
  textContent.value = ''
  error.value = ''
}

// 暴露给父组件的方法
defineExpose({
  setInputMode,
  clearTextContent,
  getInputMode: () => inputMode.value,
  getTextContent: () => textContent.value,
  setTextContent: (content) => { textContent.value = content }
})
</script>

<style scoped>
.data-input-section {
  margin-bottom: 20px;
}

/* 切换按钮样式 */
.input-mode-toggle {
  margin-bottom: 15px;
}

.toggle-buttons {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #666666;
}

.toggle-btn {
  flex: 1;
  padding: 12px 16px;
  background: rgba(40, 40, 40, 0.8);
  border: none;
  color: #cccccc;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 1px solid #666666;
}

.toggle-btn:last-child {
  border-right: none;
}

.toggle-btn:hover {
  background: rgba(60, 60, 60, 0.8);
  color: #ffffff;
}

.toggle-btn.active {
  background: #6b9bd1;
  color: #ffffff;
  font-weight: 600;
}

/* 文本输入模式样式 */
.text-input-mode {
  animation: fadeIn 0.3s ease;
}

.text-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-input-label {
  color: #cccccc;
  font-size: 14px;
  font-weight: 500;
}

.text-input-area {
  width: 100%;
  min-height: 200px;
  padding: 15px;
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid #666666;
  border-radius: 8px;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.text-input-area:focus {
  outline: none;
  border-color: #6b9bd1;
  box-shadow: 0 0 0 2px rgba(107, 155, 209, 0.2);
}

.text-input-area::placeholder {
  color: #888888;
  font-style: italic;
}

.text-input-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.parse-btn, .clear-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.parse-btn {
  background: #6b9bd1;
  color: #ffffff;
}

.parse-btn:hover:not(:disabled) {
  background: #5a8bc4;
  transform: translateY(-1px);
}

.parse-btn:disabled {
  background: #4a4a4a;
  color: #888888;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background: rgba(60, 60, 60, 0.8);
  color: #cccccc;
  border: 1px solid #666666;
}

.clear-btn:hover:not(:disabled) {
  background: rgba(80, 80, 80, 0.8);
  color: #ffffff;
  transform: translateY(-1px);
}

.clear-btn:disabled {
  background: rgba(40, 40, 40, 0.5);
  color: #666666;
  cursor: not-allowed;
  transform: none;
}

.text-input-description {
  color: #aaaaaa;
  font-size: 12px;
  font-style: italic;
  padding: 8px 12px;
  background: rgba(40, 40, 40, 0.5);
  border-radius: 6px;
  border-left: 3px solid #6b9bd1;
}

/* 文件输入模式样式 */
.file-input-mode {
  animation: fadeIn 0.3s ease;
}

/* 错误信息样式 */
.error-message {
  background: rgba(80, 40, 40, 0.6);
  border: 1px solid rgba(245, 101, 101, 0.5);
  border-radius: 6px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e53e3e;
  font-weight: 500;
  font-size: 13px;
  margin-top: 10px;
}

.error-icon {
  font-size: 1.2em;
}

.error-text {
  flex: 1;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toggle-buttons {
    flex-direction: column;
  }
  
  .toggle-btn {
    border-right: none;
    border-bottom: 1px solid #666666;
  }
  
  .toggle-btn:last-child {
    border-bottom: none;
  }
  
  .text-input-actions {
    flex-direction: column;
  }
  
  .parse-btn, .clear-btn {
    width: 100%;
  }
}
</style>

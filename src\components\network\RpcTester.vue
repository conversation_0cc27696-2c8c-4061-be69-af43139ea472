<template>
  <div class="rpc-test-section">
    <div class="test-header">
      <h4>🔍 RPC端点测试</h4>
      <div class="test-buttons">
        <button 
          @click="testConnection" 
          :disabled="isTesting || !rpcUrl" 
          class="test-btn"
        >
          <span v-if="isTesting">测试中...</span>
          <span v-else>🚀 测试连接</span>
        </button>
        <button 
          @click="refreshGasPrice" 
          :disabled="isTesting || !isConnected" 
          class="test-btn"
        >
          <span v-if="isRefreshingGas">刷新中...</span>
          <span v-else>⛽ 刷新Gas</span>
        </button>
      </div>
    </div>
    
    <StatusGrid :items="statusItems" />
    
    <div v-if="error" class="error-message">
      <span class="error-icon">❌</span>
      <span class="error-text">{{ error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import StatusGrid from '@/components/common/StatusGrid.vue'
import { useWeb3 } from '@/composables/useWeb3'
import { formatGasPrice } from '@/utils/formatters'

const props = defineProps({
  rpcUrl: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['connection-status', 'gas-price-update'])

const {
  isTestingRpc: isTesting,
  rpcConnectionStatus,
  currentBlockNumber,
  networkId,
  currentGasPrice,
  rpcTestError: error,
  testRpcConnection,
  refreshGasPrice: refreshGas
} = useWeb3()

const isRefreshingGas = ref(false)

const isConnected = computed(() => rpcConnectionStatus.value === true)

const statusItems = computed(() => [
  {
    label: '状态',
    value: getConnectionStatusText(),
    status: getConnectionStatus()
  },
  {
    label: '当前区块',
    value: currentBlockNumber.value || '未获取',
    status: isTesting.value ? 'loading' : undefined
  },
  {
    label: '实时Gas',
    value: formatGasPrice(currentGasPrice.value),
    status: getGasStatus()
  }
])

function getConnectionStatusText() {
  if (rpcConnectionStatus.value === null) return '未测试'
  return rpcConnectionStatus.value ? '已连接' : '连接失败'
}

function getConnectionStatus() {
  if (rpcConnectionStatus.value === null) return 'warning'
  return rpcConnectionStatus.value ? 'success' : 'error'
}

function getGasStatus() {
  if (isRefreshingGas.value) return 'loading'
  if (currentGasPrice.value > 50) return 'gas-high' // 假设50 Gwei为高Gas费
  return currentGasPrice.value > 0 ? 'success' : undefined
}

async function testConnection() {
  const success = await testRpcConnection(props.rpcUrl)
  emit('connection-status', success)
}

async function refreshGasPrice() {
  isRefreshingGas.value = true
  try {
    console.log('开始刷新Gas价格...')
    const success = await refreshGas()
    console.log('刷新Gas结果:', { success, currentGasPrice: currentGasPrice.value })
    
    if (success) {
      emit('gas-price-update', currentGasPrice.value)
      console.log('Gas价格更新成功:', currentGasPrice.value)
    } else {
      console.log('Gas价格更新失败')
    }
  } catch (error) {
    console.error('刷新Gas价格出错:', error)
  } finally {
    isRefreshingGas.value = false
  }
}

// 自动测试连接
async function autoTestConnection() {
  if (props.rpcUrl && !isTesting.value) {
    await testConnection()
  }
}

// 监听RPC URL变化，自动测试连接
watch(() => props.rpcUrl, async (newUrl) => {
  if (newUrl) {
    await autoTestConnection()
  }
}, { immediate: false })

// 组件挂载时自动测试连接
onMounted(async () => {
  await autoTestConnection()
})
</script>

<style scoped>
.rpc-test-section {
  background: rgba(60, 60, 60, 0.6);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid rgba(120, 120, 120, 0.3);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.test-header h4 {
  color: #ffffff;
  margin: 0;
  font-size: 16px;
}

.test-buttons {
  display: flex;
  gap: 10px;
}

.test-btn {
  background: linear-gradient(135deg, #6b9bd1 0%, #557ca6 100%);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(107, 155, 209, 0.3);
}

.test-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 155, 209, 0.4);
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  background: rgba(80, 40, 40, 0.6);
  border: 1px solid rgba(245, 101, 101, 0.5);
  border-radius: 6px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e53e3e;
  font-weight: 500;
  font-size: 13px;
  margin-top: 15px;
}

.error-icon {
  font-size: 1.2em;
}

.error-text {
  flex: 1;
}

@media (max-width: 768px) {
  .test-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .test-buttons {
    justify-content: center;
  }
  
  .test-btn {
    flex: 1;
    min-width: 0;
  }
}
</style> 
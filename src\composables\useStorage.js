import { watch, onMounted } from 'vue'

/**
 * 通用的localStorage数据持久化composable
 * @param {string} storageKey - localStorage的键名
 * @param {object} dataRefs - 需要持久化的响应式数据对象
 * @param {object} defaultValues - 默认值对象
 */
export function useStorage(storageKey, dataRefs, defaultValues = {}) {
  // 保存数据到localStorage
  function saveToStorage() {
    const dataToSave = {}
    Object.keys(dataRefs).forEach(key => {
      dataToSave[key] = dataRefs[key].value
    })
    
    try {
      localStorage.setItem(storageKey, JSON.stringify(dataToSave))
    } catch (error) {
      console.warn(`保存数据到localStorage失败:`, error)
    }
  }

  // 从localStorage加载数据
  function loadFromStorage() {
    try {
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        const data = JSON.parse(saved)
        Object.keys(dataRefs).forEach(key => {
          if (data.hasOwnProperty(key)) {
            dataRefs[key].value = data[key]
          } else if (defaultValues.hasOwnProperty(key)) {
            dataRefs[key].value = defaultValues[key]
          }
        })
      } else {
        // 应用默认值
        Object.keys(defaultValues).forEach(key => {
          if (dataRefs[key]) {
            dataRefs[key].value = defaultValues[key]
          }
        })
      }
    } catch (error) {
      console.warn(`从localStorage加载数据失败:`, error)
      // 发生错误时应用默认值
      Object.keys(defaultValues).forEach(key => {
        if (dataRefs[key]) {
          dataRefs[key].value = defaultValues[key]
        }
      })
    }
  }

  // 清除存储的数据
  function clearStoredData() {
    return new Promise((resolve, reject) => {
      if (confirm('确定要清除所有保存的数据吗？这将重置所有配置。')) {
        try {
          localStorage.removeItem(storageKey)
          // 重置所有数据到默认值
          Object.keys(defaultValues).forEach(key => {
            if (dataRefs[key]) {
              dataRefs[key].value = defaultValues[key]
            }
          })
          alert('数据已清除')
          resolve()
        } catch (error) {
          console.error('清除数据失败:', error)
          alert('清除数据失败: ' + error.message)
          reject(error)
        }
      } else {
        reject(new Error('用户取消操作'))
      }
    })
  }

  // 监听数据变化并自动保存
  const refsArray = Object.values(dataRefs)
  watch(refsArray, saveToStorage, { deep: true })

  // 组件挂载时加载数据
  onMounted(() => {
    loadFromStorage()
  })

  return {
    saveToStorage,
    loadFromStorage,
    clearStoredData
  }
} 
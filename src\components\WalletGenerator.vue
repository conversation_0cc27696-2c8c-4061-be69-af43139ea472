<template>
  <BaseLayout layout-class="wallet-generator">
    <!-- 左侧配置面板 -->
    <template #left>
      <BaseCard title="生成模式" variant="config">
        <div class="radio-group">
          <label>
            <input type="radio" v-model="useMnemonic" :value="true"/>
            <span>助记词生成模式</span>
            <small>基于单个助记词生成多个HD钱包</small>
          </label>
          <label>
            <input type="radio" v-model="useMnemonic" :value="false"/>
            <span>随机生成模式</span>
            <small>每个钱包都是完全随机生成</small>
          </label>
        </div>
      </BaseCard>

      <BaseCard title="钱包配置" variant="config">
        <FormInput
            v-model="numWallets"
            label="钱包数量"
            type="number"
            :min="1"
            :max="10000"
        />

        <div class="checkbox-group">
          <label>
            <input type="checkbox" v-model="needAmount"/>
            <span>包含ETH数量</span>
          </label>
        </div>

        <FormInput
            v-if="needAmount"
            v-model="ethAmount"
            label="ETH数量"
            type="number"
            :step="0.000000001"
            :min="0"
        />

        <ActionButtonGroup>
          <template #buttons>
            <BaseButton
                variant="primary"
                :loading="isGenerating"
                loading-text="生成中..."
                @click="handleGenerateWallets"
            >
              🚀 生成钱包
            </BaseButton>

            <BaseButton
                variant="danger"
                @click="handleClearData"
            >
              🗑️ 清除保存的数据
            </BaseButton>
          </template>
        </ActionButtonGroup>
      </BaseCard>

      <!-- 进度显示 -->
      <BaseCard v-if="isGenerating" title="生成进度" variant="status">
        <ProgressBar
            :current="currentWallet"
            :total="numWallets"
            :message="`正在生成钱包... ${currentWallet}/${numWallets}`"
        />
      </BaseCard>
    </template>

    <!-- 中间结果面板 -->
    <template #middle>
      <BaseCard v-if="generatedData.addresses.length > 0" title="✅ 生成完成！">
        <!-- 助记词显示 -->
        <div v-if="useMnemonic && generatedData.mnemonic" class="mnemonic-section">
          <h4>🔑 助记词</h4>
          <div class="mnemonic-display">
            <code>{{ generatedData.mnemonic }}</code>
            <BaseButton size="small" variant="primary" @click="copyMnemonic">
              复制
            </BaseButton>
          </div>
          <p class="warning">⚠️ 请妥善保存助记词，它可以恢复所有生成的钱包</p>
        </div>

        <!-- 统计信息 -->
        <StatusGrid :items="statsItems"/>

        <!-- 下载区域 -->
        <div class="download-section">
          <h4>📥 下载文件</h4>
          <div class="button-group">
            <BaseButton
                size="small"
                variant="success"
                @click="downloadAddresses"
            >
              📄 下载地址列表 (address.txt)
            </BaseButton>

            <BaseButton
                size="small"
                variant="success"
                @click="downloadWallets"
            >
              🔐 下载钱包数据 (address-w.txt)
            </BaseButton>

            <BaseButton
                v-if="useMnemonic && generatedData.mnemonic"
                size="small"
                variant="success"
                @click="downloadMnemonic"
            >
              🔑 下载助记词 (mnemonic.txt)
            </BaseButton>
          </div>

          <!-- 批量分发提示 -->
          <div class="tip">
            <div class="tip-icon">💡</div>
            <div class="tip-content">
              <h5>批量分发提示</h5>
              <p>下载地址列表 (address.txt)后去<span class="highlight">批量分发</span>页面导入分发</p>
              <p>下载钱包数据 (address-w.txt)去<span class="highlight">合约交互</span>页面导入交互</p>
              <p>复制地址预览中的内容，前往
                <a href="https://disperse.app/" target="_blank" rel="noopener noreferrer">
                  Disperse.app
                </a>
                进行批量ETH或代币分发</p>

            </div>
          </div>
        </div>
      </BaseCard>
    </template>

    <!-- 右侧预览面板 -->
    <template #right>
      <BaseCard v-if="generatedData.addresses.length > 0" title="📋 地址预览">
        <template #actions>
          <BaseButton
              size="small"
              variant="primary"
              @click="copyAllAddresses"
          >
            📋 全部复制
          </BaseButton>
        </template>

        <div class="address-content scrollable">
          <div class="address-list">
            <div v-for="(addr, index) in generatedData.addresses" :key="index" class="address-line">
              <span class="line-number">{{ index + 1 }}.</span>
              <span class="address-text">
                {{ needAmount ? `${addr}, ${formatEthAmount(ethAmount, 18)}` : addr }}
              </span>
              <BaseButton
                  size="small"
                  variant="secondary"
                  @click="copyAddress(addr)"
                  :title="'复制地址 ' + (index + 1)"
              >
                复制
              </BaseButton>
            </div>
          </div>
          <div v-if="generatedData.addresses.length === 0" class="empty-state">
            暂无地址数据
          </div>
        </div>
      </BaseCard>
    </template>
  </BaseLayout>
</template>

<script setup>
import {ref, computed} from 'vue'
import {Buffer} from 'buffer'

// 组件导入
import BaseLayout from '@/components/layout/BaseLayout.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import FormInput from '@/components/common/FormInput.vue'
import ActionButtonGroup from '@/components/common/ActionButtonGroup.vue'
import ProgressBar from '@/components/common/ProgressBar.vue'
import StatusGrid from '@/components/common/StatusGrid.vue'

// Composables导入
import {useStorage} from '@/composables/useStorage'
import {useWalletGeneration} from '@/composables/useWalletGeneration'
import {useFileHandler} from '@/composables/useFileHandler'

// 工具函数导入
import {STORAGE_KEYS, DEFAULT_VALUES} from '@/utils/constants'
import {formatEthAmount} from '@/utils/formatters'
import {copyToClipboard} from '@/utils/helpers'

// 确保全局 polyfills 可用
if (typeof global === 'undefined') {
  var global = globalThis;
}

// 确保 Buffer 在全局可用
window.Buffer = Buffer

// 响应式数据
const useMnemonic = ref(true)
const numWallets = ref(50)
const needAmount = ref(true)
const ethAmount = ref(0.0001)

const generatedData = ref({
  addresses: [],
  privateKeys: [],
  mnemonic: ''
})

// 使用composables
const {isGenerating, currentWallet, generateWallets} = useWalletGeneration()
const {downloadFile} = useFileHandler()
const {clearStoredData} = useStorage(
    STORAGE_KEYS.WALLET_GENERATOR,
    {
      useMnemonic,
      numWallets,
      needAmount,
      ethAmount,
      generatedData
    },
    DEFAULT_VALUES.WALLET_GENERATOR
)

// 计算属性
const statsItems = computed(() => [
  {
    label: '生成钱包数量',
    value: generatedData.value.addresses.length.toString(),
    status: 'success'
  },
  {
    label: '生成模式',
    value: useMnemonic.value ? '助记词模式' : '随机模式',
    status: 'success'
  }
])

// 生成钱包
async function handleGenerateWallets() {
  try {
    // 重置数据
    generatedData.value = {
      addresses: [],
      privateKeys: [],
      mnemonic: ''
    }

    const result = await generateWallets({
      useMnemonic: useMnemonic.value,
      numWallets: numWallets.value,
      onProgress: (current, total) => {
        // 进度更新由composable内部处理
      }
    })

    if (result) {
      generatedData.value = result
    }
  } catch (error) {
    console.error('生成钱包时出错:', error)
    alert('生成钱包时出错: ' + error.message)
  }
}

// 清除数据
async function handleClearData() {
  try {
    await clearStoredData()
    generatedData.value = {
      addresses: [],
      privateKeys: [],
      mnemonic: ''
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 下载地址列表
function downloadAddresses() {
  let content = ''
  generatedData.value.addresses.forEach(address => {
    if (needAmount.value) {
      content += `${address}, ${formatEthAmount(ethAmount.value, 18)}\n`
    } else {
      content += `${address}\n`
    }
  })

  downloadFile(content, 'address.txt')
}

// 下载钱包数据
function downloadWallets() {
  let content = ''
  generatedData.value.privateKeys.forEach(wallet => {
    content += `${wallet.privateKey}\n`
  })

  downloadFile(content, 'address-w.txt')
}

// 下载助记词
function downloadMnemonic() {
  if (generatedData.value.mnemonic) {
    downloadFile(generatedData.value.mnemonic + '\n', 'mnemonic.txt')
  }
}

// 复制助记词
async function copyMnemonic() {
  await copyToClipboard(generatedData.value.mnemonic, '助记词已复制到剪贴板')
}

// 复制地址
async function copyAddress(address) {
  await copyToClipboard(address, '地址已复制到剪贴板')
}

// 复制所有地址
async function copyAllAddresses() {
  try {
    let content = ''
    generatedData.value.addresses.forEach(address => {
      if (needAmount.value) {
        content += `${address}, ${formatEthAmount(ethAmount.value, 18)}\n`
      } else {
        content += `${address}\n`
      }
    })

    await copyToClipboard(content.trim(), `已复制 ${generatedData.value.addresses.length} 个地址到剪贴板`)
  } catch (err) {
    console.error('复制失败:', err)
    alert('复制失败: ' + err.message)
  }
}
</script>

<style scoped>
/* 生成模式样式 */
.radio-group label {
  display: block;
  margin-bottom: 12px;
  cursor: pointer;
  font-weight: 500;
  color: #ffffff;
}

.radio-group input[type="radio"] {
  margin-right: 8px;
}

.radio-group small {
  display: block;
  color: #cccccc;
  font-size: 13px;
  margin-left: 24px;
  font-weight: normal;
}

.checkbox-group {
  margin-bottom: 15px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #ffffff;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
}

/* 助记词区域样式 */
.mnemonic-section {
  background: rgba(60, 30, 30, 0.8);
  border: 1px solid rgba(200, 100, 100, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.mnemonic-display {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.mnemonic-display code {
  flex: 1;
  background: rgba(20, 20, 20, 0.8);
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  color: #ffffff;
}

.warning {
  color: #ff6b6b;
  font-weight: 500;
  font-size: 14px;
}

/* 下载区域样式 */
.download-section h4 {
  margin-bottom: 15px;
  color: #ffffff;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

/* 提示区域样式 */
.tip {
  display: flex;
  gap: 10px;
  background: rgba(60, 60, 60, 0.6);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #6b9bd1;
}

.tip-icon {
  font-size: 1.2em;
  flex-shrink: 0;
}

.tip-content h5 {
  color: #ffffff;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.tip-content p {
  color: #cccccc;
  margin: 0 0 10px 0;
  font-size: 13px;
  line-height: 1.4;
}

.tip-content p:last-child {
  margin-bottom: 0;
}

.tip-content .highlight {
  color: #6b9bd1;
  font-weight: bold;
  background: rgba(107, 155, 209, 0.2);
  padding: 2px 4px;
  border-radius: 3px;
}

.tip-content a {
  color: #6b9bd1;
  text-decoration: none;
}

.tip-content a:hover {
  text-decoration: underline;
}

/* 预览区域样式 */
.address-content {
  max-height: 400px;
  overflow-y: auto;
  background: rgba(20, 20, 20, 0.6);
  border-radius: 8px;
  padding: 15px;
}

.address-list {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.address-line {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(120, 120, 120, 0.2);
  transition: background 0.2s ease;
  gap: 10px;
}

.address-line:last-child {
  border-bottom: none;
}

.address-line:hover {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.line-number {
  min-width: 40px;
  text-align: right;
  color: #888;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.address-text {
  flex: 1;
  word-break: break-all;
  color: #ffffff;
  min-width: 0;
}

.empty-state {
  text-align: center;
  color: #cccccc;
  font-style: italic;
  padding: 40px 20px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .address-content {
    max-height: 250px;
  }

  .address-line {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    padding: 10px 0;
  }

  .line-number {
    min-width: auto;
    align-self: flex-start;
  }

  .address-text {
    width: 100%;
    word-break: break-all;
  }
}

@media (max-width: 480px) {
  .address-content {
    max-height: 200px;
  }

  .address-line {
    font-size: 12px;
    padding: 8px 0;
  }

  .line-number {
    font-size: 11px;
  }

  .address-text {
    font-size: 12px;
  }
}
</style> 
<template>
  <div class="format-item">
    <strong>{{ title }}</strong>
    <div class="format-examples">
      <code v-for="(example, index) in examples" :key="index">
        {{ example }}
      </code>
    </div>
    <small v-if="description">{{ description }}</small>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  examples: {
    type: Array,
    default: () => []
  },
  description: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.format-item {
  background: rgba(20, 20, 20, 0.6);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid rgba(120, 120, 120, 0.3);
}

.format-item strong {
  color: #ffffff;
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
}

.format-examples {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.format-item code {
  display: block;
  background: rgba(40, 40, 40, 0.8);
  padding: 6px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #ffffff;
  word-break: break-all;
}

.format-item small {
  color: #cccccc;
  font-style: italic;
  margin-top: 6px;
  display: block;
}
</style>
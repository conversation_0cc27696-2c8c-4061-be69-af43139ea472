/* 共享的基础样式 - 保留必要的样式，移除已被组件替代的重复样式 */

/* 保留用于向后兼容的基础样式 */
.progress-section {
  text-align: center;
  color: #ffffff;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #333333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6b9bd1, #8db4e2);
  transition: width 0.3s;
  border-radius: 4px;
}

/* 状态显示通用样式 - 保留StatusGrid组件使用 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-item {
  background: rgba(60, 60, 60, 0.6);
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  color: #cccccc;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
  color: #ffffff;
}

.status-value.success {
  color: #27ae60;
}

.status-value.error {
  color: #e53e3e;
}

.status-value.warning {
  color: #d68910;
}

.status-value.gas-high {
  color: #e53e3e;
}

.status-value.loading {
  color: #d68910;
}

/* 滚动条样式 - 保留通用的滚动条样式 */
.scrollable::-webkit-scrollbar {
  width: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: rgba(60, 60, 60, 0.3);
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 120, 0.6);
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
} 
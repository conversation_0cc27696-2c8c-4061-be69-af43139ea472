import { ref } from 'vue'
import Web3 from 'web3'

/**
 * Web3相关操作的composable
 */
export function useWeb3() {
  const web3 = ref(null)
  const isTestingRpc = ref(false)
  const rpcConnectionStatus = ref(null) // null: 未测试, true: 连接成功, false: 连接失败
  const currentBlockNumber = ref(null)
  const networkId = ref(null)
  const currentGasPrice = ref(0)
  const rpcTestError = ref('')

  // 初始化Web3
  function initWeb3(rpcUrl) {
    try {
      web3.value = new Web3(rpcUrl)
      console.log('Web3 连接成功')
      return true
    } catch (error) {
      console.error('Web3 连接失败:', error)
      return false
    }
  }

  // 测试RPC连接
  async function testRpcConnection(rpcUrl) {
    if (!rpcUrl) {
      rpcTestError.value = '请先输入RPC端点'
      return false
    }

    isTestingRpc.value = true
    rpcTestError.value = ''
    
    try {
      // 初始化Web3连接
      const testWeb3 = new Web3(rpcUrl)
      
      // 测试1: 获取网络ID
      console.log('正在获取网络ID...')
      const chainId = await testWeb3.eth.getChainId()
      networkId.value = chainId.toString()
      
      // 测试2: 获取当前区块号
      console.log('正在获取当前区块号...')
      const blockNumber = await testWeb3.eth.getBlockNumber()
      currentBlockNumber.value = blockNumber.toString()
      
      // 测试3: 获取Gas价格
      console.log('正在获取Gas价格...')
      const gasPrice = await testWeb3.eth.getGasPrice()
      const gasPriceInGwei = parseFloat(testWeb3.utils.fromWei(gasPrice, 'gwei'))
      currentGasPrice.value = gasPriceInGwei
      
      // 更新Web3实例
      web3.value = testWeb3
      rpcConnectionStatus.value = true
      
      console.log('RPC连接测试成功:', {
        networkId: networkId.value,
        blockNumber: currentBlockNumber.value,
        gasPrice: currentGasPrice.value + ' Gwei'
      })
      
      return true
      
    } catch (error) {
      console.error('RPC连接测试失败:', error)
      rpcConnectionStatus.value = false
      rpcTestError.value = `连接失败: ${error.message}`
      
      // 重置数据
      networkId.value = null
      currentBlockNumber.value = null
      currentGasPrice.value = 0
      
      return false
    } finally {
      isTestingRpc.value = false
    }
  }

  // 获取当前Gas价格
  async function getCurrentGasPrice() {
    try {
      if (!web3.value) return 0
      const gasPrice = await web3.value.eth.getGasPrice()
      return parseFloat(web3.value.utils.fromWei(gasPrice, 'gwei'))
    } catch (error) {
      console.error('获取Gas价格失败:', error)
      return 0
    }
  }

  // 刷新Gas价格
  async function refreshGasPrice() {
    if (!web3.value || !rpcConnectionStatus.value) {
      rpcTestError.value = '请先测试RPC连接'
      return false
    }

    rpcTestError.value = ''
    
    try {
      console.log('正在刷新Gas价格...')
      const gasPrice = await web3.value.eth.getGasPrice()
      const gasPriceInGwei = parseFloat(web3.value.utils.fromWei(gasPrice, 'gwei'))
      currentGasPrice.value = gasPriceInGwei
      
      console.log('Gas价格刷新成功:', currentGasPrice.value + ' Gwei')
      return true
      
    } catch (error) {
      console.error('刷新Gas价格失败:', error)
      rpcTestError.value = `刷新失败: ${error.message}`
      currentGasPrice.value = 0
      return false
    }
  }

  // 获取钱包余额
  async function getBalance(address) {
    try {
      if (!web3.value) return null
      const balance = await web3.value.eth.getBalance(address)
      return parseFloat(web3.value.utils.fromWei(balance, 'ether')).toFixed(4)
    } catch (error) {
      console.warn(`获取地址 ${address} 余额失败:`, error)
      return '获取失败'
    }
  }

  // 批量获取钱包余额
  async function loadWalletBalances(wallets) {
    if (!web3.value) {
      console.warn('Web3未初始化，无法获取余额')
      return wallets
    }

    try {
      const updatedWallets = [...wallets]
      for (let i = 0; i < updatedWallets.length; i++) {
        const balance = await getBalance(updatedWallets[i].address)
        updatedWallets[i].balance = balance
      }
      return updatedWallets
    } catch (error) {
      console.error('获取余额过程中出错:', error)
      return wallets
    }
  }

  // 重置连接状态
  function resetConnectionStatus() {
    rpcConnectionStatus.value = null
    currentBlockNumber.value = null
    networkId.value = null
    currentGasPrice.value = 0
    rpcTestError.value = ''
  }

  // 获取连接状态显示文本
  function getConnectionStatusText() {
    if (rpcConnectionStatus.value === null) {
      return '未测试'
    } else if (rpcConnectionStatus.value === true) {
      return '已连接'
    } else {
      return '连接失败'
    }
  }

  // 获取连接状态样式类
  function getConnectionStatusClass() {
    if (rpcConnectionStatus.value === null) {
      return 'status-unknown'
    } else if (rpcConnectionStatus.value === true) {
      return 'status-success'
    } else {
      return 'status-error'
    }
  }

  // ERC20代币标准ABI
  const ERC20_ABI = [
    {
      "constant": true,
      "inputs": [],
      "name": "name",
      "outputs": [{"name": "", "type": "string"}],
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "symbol",
      "outputs": [{"name": "", "type": "string"}],
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "decimals",
      "outputs": [{"name": "", "type": "uint8"}],
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [{"name": "_owner", "type": "address"}],
      "name": "balanceOf",
      "outputs": [{"name": "balance", "type": "uint256"}],
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [
        {"name": "_to", "type": "address"},
        {"name": "_value", "type": "uint256"}
      ],
      "name": "transfer",
      "outputs": [{"name": "", "type": "bool"}],
      "type": "function"
    }
  ]

  // 获取ERC20代币信息
  async function getTokenInfo(tokenAddress) {
    try {
      if (!web3.value || !tokenAddress) return null

      const contract = new web3.value.eth.Contract(ERC20_ABI, tokenAddress)

      const [name, symbol, decimals] = await Promise.all([
        contract.methods.name().call(),
        contract.methods.symbol().call(),
        contract.methods.decimals().call()
      ])

      return {
        address: tokenAddress,
        name,
        symbol,
        decimals: parseInt(decimals)
      }
    } catch (error) {
      console.error('获取代币信息失败:', error)
      return null
    }
  }

  // 获取ERC20代币余额
  async function getTokenBalance(tokenAddress, walletAddress) {
    try {
      if (!web3.value || !tokenAddress || !walletAddress) return '0'

      const contract = new web3.value.eth.Contract(ERC20_ABI, tokenAddress)
      const balance = await contract.methods.balanceOf(walletAddress).call()

      return balance
    } catch (error) {
      console.warn(`获取代币余额失败 ${walletAddress}:`, error)
      return '0'
    }
  }

  // 格式化代币余额（从wei转换为可读格式）
  function formatTokenBalance(balance, decimals) {
    try {
      if (!balance || balance === '0') return '0'
      const divisor = BigInt(10 ** decimals)
      const balanceBigInt = BigInt(balance)
      const integerPart = balanceBigInt / divisor
      const fractionalPart = balanceBigInt % divisor

      if (fractionalPart === BigInt(0)) {
        return integerPart.toString()
      }

      const fractionalStr = fractionalPart.toString().padStart(decimals, '0')
      const trimmedFractional = fractionalStr.replace(/0+$/, '')

      if (trimmedFractional === '') {
        return integerPart.toString()
      }

      return `${integerPart}.${trimmedFractional}`
    } catch (error) {
      console.error('格式化代币余额失败:', error)
      return '0'
    }
  }

  // 将代币数量转换为wei格式
  function parseTokenAmount(amount, decimals) {
    try {
      if (!amount || amount === '0') return '0'
      const multiplier = BigInt(10 ** decimals)
      const amountStr = amount.toString()

      if (amountStr.includes('.')) {
        const [integerPart, fractionalPart] = amountStr.split('.')
        const paddedFractional = fractionalPart.padEnd(decimals, '0').slice(0, decimals)
        const totalAmount = BigInt(integerPart || '0') * multiplier + BigInt(paddedFractional || '0')
        return totalAmount.toString()
      } else {
        return (BigInt(amountStr) * multiplier).toString()
      }
    } catch (error) {
      console.error('解析代币数量失败:', error)
      return '0'
    }
  }

  // 批量获取钱包的代币余额
  async function loadWalletTokenBalances(wallets, tokenAddress, tokenDecimals) {
    if (!web3.value || !tokenAddress) {
      console.warn('Web3未初始化或代币地址为空，无法获取代币余额')
      return wallets
    }

    try {
      const updatedWallets = [...wallets]
      for (let i = 0; i < updatedWallets.length; i++) {
        const balance = await getTokenBalance(tokenAddress, updatedWallets[i].address)
        const formattedBalance = formatTokenBalance(balance, tokenDecimals)
        updatedWallets[i].tokenBalance = formattedBalance
        updatedWallets[i].tokenBalanceWei = balance
      }
      return updatedWallets
    } catch (error) {
      console.error('获取代币余额过程中出错:', error)
      return wallets
    }
  }

  // 执行ERC20代币转账
  async function executeTokenTransfer(wallet, tokenAddress, targetAddress, amount, gasLimit, gasPrice, additionalGas = 0) {
    try {
      if (!web3.value) {
        throw new Error('Web3未初始化')
      }

      console.log('执行代币转账，使用钱包:', wallet.address)

      // 检查ETH余额（用于支付gas费）
      const ethBalance = await web3.value.eth.getBalance(wallet.address)
      if (ethBalance === '0') {
        throw new Error('钱包ETH余额不足，无法支付Gas费用')
      }

      // 创建合约实例
      const contract = new web3.value.eth.Contract(ERC20_ABI, tokenAddress)

      // 构建转账数据
      const transferData = contract.methods.transfer(targetAddress, amount).encodeABI()

      // 获取当前gas价格并添加额外gas
      const currentGasPriceBN = await web3.value.eth.getGasPrice()
      const additionalGasWei = web3.value.utils.toWei(additionalGas.toString(), 'gwei')
      const finalGasPrice = (BigInt(currentGasPriceBN) + BigInt(additionalGasWei)).toString()

      // 获取nonce
      const nonce = await web3.value.eth.getTransactionCount(wallet.address, 'pending')

      // 获取链ID
      const chainId = await web3.value.eth.getChainId()

      // 构造交易
      const transaction = {
        from: wallet.address,
        to: tokenAddress,
        value: '0', // ERC20转账不需要发送ETH
        gas: gasLimit,
        gasPrice: finalGasPrice,
        nonce: nonce,
        chainId: chainId,
        data: transferData
      }

      // 签名交易
      const signedTransaction = await web3.value.eth.accounts.signTransaction(transaction, wallet.privateKey)

      // 从rawTransaction计算交易hash
      const txHash = web3.value.utils.keccak256(signedTransaction.rawTransaction)

      // 发送交易并处理结果
      return await new Promise((resolve) => {
        web3.value.eth.sendSignedTransaction(signedTransaction.rawTransaction)
          .on('transactionHash', function(hash) {
            resolve({
              success: true,
              txHash: hash,
              address: wallet.address
            })
          })
          .on('error', function(error) {
            const errorMessage = error.message.toLowerCase()

            if (errorMessage.includes('already known') || errorMessage.includes('known transaction')) {
              console.log(`交易已在内存池中: ${wallet.address}`)
              resolve({
                success: true,
                txHash: txHash,
                address: wallet.address,
                note: '交易已在内存池中'
              })
            } else {
              console.error('代币转账失败:', error.message)
              resolve({
                success: false,
                error: `代币转账失败: ${error.message}`,
                txHash: txHash,
                address: wallet.address
              })
            }
          })
      })
    } catch (error) {
      console.error('执行代币转账失败:', error)
      return {
        success: false,
        error: error.message,
        address: wallet.address
      }
    }
  }

  return {
    web3,
    isTestingRpc,
    rpcConnectionStatus,
    currentBlockNumber,
    networkId,
    currentGasPrice,
    rpcTestError,
    initWeb3,
    testRpcConnection,
    getCurrentGasPrice,
    refreshGasPrice,
    getBalance,
    loadWalletBalances,
    resetConnectionStatus,
    getConnectionStatusText,
    getConnectionStatusClass,
    // ERC20相关功能
    getTokenInfo,
    getTokenBalance,
    formatTokenBalance,
    parseTokenAmount,
    loadWalletTokenBalances,
    executeTokenTransfer
  }
} 
{"name": "ak47tool", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ethereumjs/wallet": "^10.0.0", "bip39": "^3.1.0", "buffer": "^6.0.3", "ethereumjs-wallet": "^1.0.2", "ethers": "^6.15.0", "vue": "^3.5.18", "web3": "^4.16.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "^7.0.6", "vite-plugin-node-polyfills": "^0.24.0", "vite-plugin-vue-devtools": "^8.0.0"}}
<template>
  <div class="network-selector">
    <div class="input-group">
      <label>选择网络:</label>
      <select 
        v-model="selectedNetwork" 
        @change="onNetworkChange" 
        class="network-select"
      >
        <option value="">自定义网络</option>
        <option 
          v-for="network in networks" 
          :key="network.name" 
          :value="network.name"
        >
          {{ network.name }}
        </option>
      </select>
    </div>
    <div class="input-group">
      <label>RPC端点:</label>
      <input 
        type="text" 
        v-model="rpcUrl" 
        placeholder="输入RPC端点或从上面选择网络"
        class="wide-input"
        @input="onRpcUrlChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { NETWORKS } from '@/utils/constants'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  defaultNetwork: {
    type: String,
    default: '以太坊测试网'
  }
})

const emit = defineEmits(['update:modelValue', 'network-change', 'rpc-change'])

const networks = NETWORKS
const selectedNetwork = ref(props.defaultNetwork)
const rpcUrl = ref(props.modelValue)

// 初始化时设置默认网络的RPC URL
if (!rpcUrl.value && selectedNetwork.value) {
  const network = networks.find(n => n.name === selectedNetwork.value)
  if (network) {
    rpcUrl.value = network.url
  }
}

function onNetworkChange() {
  const network = networks.find(n => n.name === selectedNetwork.value)
  if (network) {
    rpcUrl.value = network.url
  }
  emit('network-change', {
    network: selectedNetwork.value,
    url: rpcUrl.value
  })
}

function onRpcUrlChange() {
  emit('update:modelValue', rpcUrl.value)
  emit('rpc-change', rpcUrl.value)
}

// 监听外部的RPC URL变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== rpcUrl.value) {
    rpcUrl.value = newValue
  }
})

// 发出初始值
emit('update:modelValue', rpcUrl.value)
</script>

<style scoped>
.network-selector {
  background: rgba(60, 60, 60, 0.6);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group label {
  min-width: 100px;
  font-weight: 500;
  color: #ffffff;
}

.network-select {
  padding: 8px 12px;
  border: 1px solid #555;
  border-radius: 6px;
  font-size: 14px;
  background: rgba(60, 60, 60, 0.8);
  color: #ffffff;
  width: 100%;
  cursor: pointer;
}

.network-select option {
  background: #333333;
  color: #ffffff;
}

.network-select:focus {
  outline: none;
  border-color: #6b9bd1;
  box-shadow: 0 0 0 2px rgba(107, 155, 209, 0.2);
}

.wide-input {
  flex: 1;
  min-width: 0;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #555;
  border-radius: 6px;
  font-size: 14px;
  background: rgba(60, 60, 60, 0.8);
  color: #ffffff;
}

.wide-input:focus {
  outline: none;
  border-color: #6b9bd1;
  box-shadow: 0 0 0 2px rgba(107, 155, 209, 0.3);
}

@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .input-group label {
    min-width: auto;
    margin-bottom: 5px;
  }
  
  .network-select,
  .wide-input {
    width: 100%;
    min-width: auto;
  }
}
</style> 
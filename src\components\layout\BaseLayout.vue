<template>
  <div class="base-layout" :class="layoutClass">
    <div class="container">
      <div class="main-content" :class="{ 'single-column': singleColumn, 'two-column': twoColumn }">
        <div v-if="$slots.left" class="left-panel">
          <slot name="left"></slot>
        </div>
        <div v-if="$slots.middle" class="middle-panel">
          <slot name="middle"></slot>
        </div>
        <div v-if="$slots.right" class="right-panel">
          <slot name="right"></slot>
        </div>
        <!-- 单栏布局时使用的默认插槽 -->
        <div v-if="singleColumn && $slots.default" class="single-panel">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  layoutClass: {
    type: String,
    default: ''
  },
  singleColumn: {
    type: Boolean,
    default: false
  },
  twoColumn: {
    type: Boolean,
    default: false
  }
})

// 计算是否为三栏布局
const isThreeColumn = computed(() => !props.singleColumn && !props.twoColumn)
</script>

<style scoped>
.base-layout {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  margin: 0;
  box-sizing: border-box;
  background: transparent;
}

.container {
  width: 100%;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
}

/* 主要内容区域水平布局 */
.main-content {
  display: flex;
  gap: 25px;
  align-items: flex-start;
}

/* 三栏布局（默认） */
.left-panel,
.middle-panel,
.right-panel {
  flex: 1;
  min-width: 0;
  flex-basis: 0;
}

/* 两栏布局 */
.main-content.two-column .left-panel,
.main-content.two-column .right-panel {
  flex: 1;
}

.main-content.two-column .middle-panel {
  display: none;
}

/* 单栏布局 */
.main-content.single-column {
  justify-content: center;
}

.main-content.single-column .left-panel,
.main-content.single-column .middle-panel,
.main-content.single-column .right-panel {
  display: none;
}

.single-panel {
  flex: 1;
  max-width: 1200px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel, 
  .middle-panel, 
  .right-panel {
    max-width: none;
    min-width: auto;
    flex-basis: auto;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .main-content {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
  
  .main-content {
    gap: 15px;
  }
}
</style>
import { ref } from 'vue'
import { ethers } from 'ethers'

/**
 * 钱包生成相关的composable
 */
export function useWalletGeneration() {
  const isGenerating = ref(false)
  const currentWallet = ref(0)

  // 验证私钥格式
  function isValidPrivateKey(privateKey) {
    const cleanKey = privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey
    return /^[0-9a-fA-F]{64}$/.test(cleanKey) && cleanKey !== '0'.repeat(64)
  }

  // 使用助记词生成钱包
  async function generateWithMnemonic(numWallets, onProgress) {
    try {
      console.log("助记词生成模式")

      // 生成随机助记词
      const mnemonic = ethers.Wallet.createRandom().mnemonic.phrase
      console.log(`助记词: ${mnemonic}\n`)

      const addresses = []
      const privateKeys = []
      
      // 从助记词生成多个钱包
      for (let i = 0; i < numWallets; i++) {
        // 使用BIP44的派生路径格式：m/44'/60'/0'/0/{i}
        const derivationPath = `m/44'/60'/0'/0/${i}`
        const hdNode = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, derivationPath)

        const address = hdNode.address
        const privateKey = hdNode.privateKey

        addresses.push(address)
        privateKeys.push({ address, privateKey })
        
        console.log(`生成第${i + 1}个钱包，路径: ${derivationPath}, 地址: ${address}`)
        
        currentWallet.value = i + 1
        
        // 调用进度回调
        if (onProgress) {
          onProgress(i + 1, numWallets)
        }
        
        // 让UI有机会更新
        if (i % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1))
        }
      }

      console.log(`助记词生成完成，共生成${addresses.length}个唯一地址`)
      
      return {
        addresses,
        privateKeys,
        mnemonic
      }

    } catch (error) {
      console.error('创建HD钱包失败:', error)
      throw new Error('HD钱包创建失败: ' + error.message + '\n请刷新页面重试')
    }
  }

  // 随机生成钱包
  async function generateRandomWallets(numWallets, onProgress) {
    console.log('随机生成钱包模式')
    
    const generatedAddresses = new Set() // 用于检查重复地址
    const addresses = []
    const privateKeys = []
    
    for (let i = 0; i < numWallets; i++) {
      let wallet
      let attempts = 0
      
      // 确保生成不重复的钱包
      do {
        wallet = ethers.Wallet.createRandom()
        attempts++
        
        // 防止无限循环（理论上不会发生，但安全起见）
        if (attempts > 10) {
          console.warn(`生成第${i}个钱包时尝试了${attempts}次`)
          break
        }
      } while (generatedAddresses.has(wallet.address))
      
      generatedAddresses.add(wallet.address)
      
      addresses.push(wallet.address)
      privateKeys.push({
        address: wallet.address,
        privateKey: wallet.privateKey
      })
      
      currentWallet.value = i + 1
      
      // 调用进度回调
      if (onProgress) {
        onProgress(i + 1, numWallets)
      }
      
      // 让UI有机会更新
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1))
      }
    }
    
    console.log(`随机生成完成，共生成${generatedAddresses.size}个唯一地址`)
    
    return {
      addresses,
      privateKeys,
      mnemonic: ''
    }
  }

  // 主生成函数
  async function generateWallets(options) {
    const { useMnemonic, numWallets, onProgress } = options
    
    if (isGenerating.value) return null
    
    isGenerating.value = true
    currentWallet.value = 0
    
    try {
      let result
      if (useMnemonic) {
        result = await generateWithMnemonic(numWallets, onProgress)
      } else {
        result = await generateRandomWallets(numWallets, onProgress)
      }
      return result
    } catch (error) {
      console.error('生成钱包时出错:', error)
      throw error
    } finally {
      isGenerating.value = false
    }
  }

  return {
    isGenerating,
    currentWallet,
    isValidPrivateKey,
    generateWallets
  }
} 
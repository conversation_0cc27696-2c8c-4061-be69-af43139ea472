/**
 * 延迟函数
 * @param {number} seconds - 延迟秒数
 * @returns {Promise} Promise对象
 */
export function sleep(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000))
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示信息
 * @returns {Promise<boolean>} 是否复制成功
 */
export async function copyToClipboard(text, successMessage = '已复制到剪贴板') {
  try {
    await navigator.clipboard.writeText(text)
    if (successMessage) {
      alert(successMessage)
    }
    return true
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：使用传统方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      if (successMessage) {
        alert(successMessage)
      }
      return true
    } catch (fallbackErr) {
      console.error('降级复制方案也失败:', fallbackErr)
      alert('复制失败: ' + fallbackErr.message)
      return false
    }
  }
}

/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 需要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成唯一ID
 * @param {number} length - ID长度
 * @returns {string} 唯一ID
 */
export function generateId(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 格式化私钥（确保0x前缀）
 * @param {string} privateKey - 原始私钥
 * @returns {string} 格式化后的私钥
 */
export function formatPrivateKey(privateKey) {
  if (!privateKey) return ''
  return privateKey.startsWith('0x') ? privateKey : '0x' + privateKey
}

/**
 * 清理地址格式（转换为标准格式）
 * @param {string} address - 原始地址
 * @returns {string} 清理后的地址
 */
export function cleanAddress(address) {
  if (!address || typeof address !== 'string') return ''
  return address.trim().toLowerCase()
}

/**
 * 计算进度百分比
 * @param {number} current - 当前进度
 * @param {number} total - 总数
 * @returns {number} 百分比（0-100）
 */
export function calculateProgress(current, total) {
  if (total === 0) return 0
  return Math.round((current / total) * 100)
}

/**
 * 安全解析JSON
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果或默认值
 */
export function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 安全的数字转换
 * @param {any} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的数字
 */
export function safeParseNumber(value, defaultValue = 0) {
  const num = parseFloat(value)
  return isNaN(num) ? defaultValue : num
}

/**
 * 安全的整数转换
 * @param {any} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的整数
 */
export function safeParseInt(value, defaultValue = 0) {
  const num = parseInt(value)
  return isNaN(num) ? defaultValue : num
}

/**
 * 获取错误信息
 * @param {Error|string} error - 错误对象或字符串
 * @returns {string} 错误信息
 */
export function getErrorMessage(error) {
  if (!error) return '未知错误'
  
  if (typeof error === 'string') return error
  
  // 处理Web3错误
  if (error.message) {
    let message = error.message
    
    // 提取EVM错误信息
    if (message.includes('reverted')) {
      if (message.includes('Transaction has been reverted by the EVM')) {
        return 'EVM执行失败: 交易被回滚'
      }
      return '交易被回滚: ' + message
    }
    
    // 提取gas相关错误
    if (message.includes('gas')) {
      if (message.includes('out of gas')) {
        return 'Gas不足: 请增加Gas限制'
      }
      if (message.includes('gas price too low')) {
        return 'Gas价格过低'
      }
      return 'Gas相关错误: ' + message
    }
    
    // 余额不足错误
    if (message.includes('insufficient funds')) {
      return '余额不足: 请检查钱包余额'
    }
    
    // nonce错误
    if (message.includes('nonce')) {
      return 'Nonce错误: ' + message
    }
    
    // 网络连接错误
    if (message.includes('connection') || message.includes('network')) {
      return '网络连接错误: ' + message
    }
    
    return message
  }
  
  // 处理其他类型的错误
  if (error instanceof Error) return error.message
  if (error.reason) return error.reason
  if (error.code) return `错误代码: ${error.code}`
  
  return JSON.stringify(error)
} 
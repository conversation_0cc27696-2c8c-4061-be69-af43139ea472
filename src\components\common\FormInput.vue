<template>
  <div class="input-group" :class="{ 'vertical': vertical }">
    <label v-if="label" :class="{ required: required }">{{ label }}:</label>
    <div class="input-wrapper">
      <input 
        v-if="type !== 'select' && type !== 'textarea'"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :min="min"
        :max="max"
        :step="step"
        :value="modelValue"
        :class="inputClass"
        @input="handleInput"
        @change="$emit('change', $event.target.value)"
      />
      <select 
        v-else-if="type === 'select'"
        :disabled="disabled"
        :value="modelValue"
        :class="inputClass"
        @input="handleInput"
        @change="$emit('change', $event.target.value)"
      >
        <option v-if="placeholder" value="">{{ placeholder }}</option>
        <option 
          v-for="option in options" 
          :key="option.value" 
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>
      <textarea
        v-else-if="type === 'textarea'"
        :placeholder="placeholder"
        :disabled="disabled"
        :value="modelValue"
        :class="inputClass"
        :rows="rows"
        @input="handleInput"
        @change="$emit('change', $event.target.value)"
      />
      <div v-if="$slots.addon" class="input-addon">
        <slot name="addon"></slot>
      </div>
    </div>
    <small v-if="description" class="input-description">{{ description }}</small>
    <div v-if="error" class="input-error">{{ error }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  vertical: {
    type: Boolean,
    default: false
  },
  wide: {
    type: Boolean,
    default: false
  },
  min: {
    type: [String, Number],
    default: undefined
  },
  max: {
    type: [String, Number],
    default: undefined
  },
  step: {
    type: [String, Number],
    default: undefined
  },
  rows: {
    type: Number,
    default: 3
  },
  options: {
    type: Array,
    default: () => []
  },
  description: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const inputClass = computed(() => [
  'form-control',
  { 'wide-input': props.wide },
  { 'error': props.error }
])

function handleInput(event) {
  let value = event.target.value
  if (props.type === 'number') {
    value = value === '' ? 0 : Number(value)
  }
  emit('update:modelValue', value)
}
</script>

<style scoped>
.input-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.input-group.vertical {
  flex-direction: column;
  align-items: flex-start;
}

.input-group label {
  min-width: 120px;
  font-weight: 500;
  color: #ffffff;
  flex-shrink: 0;
}

.input-group.vertical label {
  min-width: auto;
  margin-bottom: 5px;
}

.input-group label.required::after {
  content: ' *';
  color: #e53e3e;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #555;
  border-radius: 6px;
  font-size: 14px;
  background: rgba(60, 60, 60, 0.8);
  color: #ffffff;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #6b9bd1;
  box-shadow: 0 0 0 2px rgba(107, 155, 209, 0.3);
}

.form-control:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(60, 60, 60, 0.4);
}

.form-control.error {
  border-color: #e53e3e;
}

.form-control.error:focus {
  box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.3);
}

.wide-input {
  flex: 1;
  min-width: 0;
  width: 100%;
}

select.form-control {
  cursor: pointer;
}

select.form-control option {
  background: #333333;
  color: #ffffff;
}

textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.input-addon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-description {
  color: #cccccc;
  font-style: italic;
  font-size: 12px;
  margin-top: 4px;
}

.input-error {
  color: #e53e3e;
  font-weight: 500;
  font-size: 12px;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .input-group label {
    min-width: auto;
    margin-bottom: 5px;
  }
  
  .input-wrapper {
    width: 100%;
  }
  
  .wide-input {
    width: 100%;
    min-width: auto;
  }
}
</style>
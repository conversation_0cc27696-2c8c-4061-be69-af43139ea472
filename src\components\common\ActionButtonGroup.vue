<template>
  <div class="action-section">
    <div class="button-group" :class="{ vertical: vertical }">
      <slot name="buttons"></slot>
    </div>
    <div v-if="$slots.tips" class="action-tips">
      <slot name="tips"></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  autoSaveTip: {
    type: Boolean,
    default: true
  },
  vertical: {
    type: <PERSON>olean,
    default: false
  }
})
</script>

<style scoped>
.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.button-group.vertical {
  flex-direction: column;
  width: 100%;
  max-width: 300px;
}

.auto-save-tip {
  color: #48bb78;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
  text-align: center;
}

.action-tips {
  text-align: center;
  color: #cccccc;
  font-size: 14px;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
    width: 100%;
  }
}
</style>
/**
 * 验证私钥格式
 * @param {string} privateKey - 私钥字符串
 * @returns {boolean} 是否为有效私钥
 */
export function isValidPrivateKey(privateKey) {
  if (!privateKey || typeof privateKey !== 'string') return false
  
  // 检查是否为64位十六进制字符串 (可以有0x前缀)
  const cleanKey = privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey
  return /^[0-9a-fA-F]{64}$/.test(cleanKey) && cleanKey !== '0'.repeat(64)
}

/**
 * 验证以太坊地址格式
 * @param {string} address - 地址字符串
 * @returns {boolean} 是否为有效地址
 */
export function isValidAddress(address) {
  if (!address || typeof address !== 'string') return false
  
  // 基本格式检查：0x开头，40位十六进制字符
  return /^0x[0-9a-fA-F]{40}$/.test(address)
}

/**
 * 验证RPC URL格式
 * @param {string} url - RPC URL
 * @returns {boolean} 是否为有效URL
 */
export function isValidRpcUrl(url) {
  if (!url || typeof url !== 'string') return false
  
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * 验证Gas限制
 * @param {number} gasLimit - Gas限制
 * @returns {boolean} 是否为有效Gas限制
 */
export function isValidGasLimit(gasLimit) {
  const num = parseFloat(gasLimit)
  return !isNaN(num) && num >= 21000 && num <= 10000000
}

/**
 * 验证Gas价格
 * @param {number} gasPrice - Gas价格（Gwei）
 * @returns {boolean} 是否为有效Gas价格
 */
export function isValidGasPrice(gasPrice) {
  const num = parseFloat(gasPrice)
  return !isNaN(num) && num >= 0 && num <= 1000
}

/**
 * 验证ETH数量
 * @param {number} amount - ETH数量
 * @returns {boolean} 是否为有效数量
 */
export function isValidEthAmount(amount) {
  const num = parseFloat(amount)
  return !isNaN(num) && num >= 0
}

/**
 * 验证钱包数量
 * @param {number} count - 钱包数量
 * @returns {boolean} 是否为有效数量
 */
export function isValidWalletCount(count) {
  const num = parseInt(count)
  return !isNaN(num) && num >= 1 && num <= 10000
}

/**
 * 验证助记词格式
 * @param {string} mnemonic - 助记词
 * @returns {boolean} 是否为有效助记词
 */
export function isValidMnemonic(mnemonic) {
  if (!mnemonic || typeof mnemonic !== 'string') return false
  
  const words = mnemonic.trim().split(/\s+/)
  // 助记词通常是12、15、18、21或24个单词
  const validLengths = [12, 15, 18, 21, 24]
  return validLengths.includes(words.length) && words.every(word => word.length > 0)
}

/**
 * 验证合约数据格式
 * @param {string} data - 合约数据
 * @returns {boolean} 是否为有效合约数据
 */
export function isValidContractData(data) {
  if (!data) return true // 空数据是有效的（普通转账）
  if (typeof data !== 'string') return false
  
  // 合约数据应该是0x开头的十六进制字符串
  return /^0x[0-9a-fA-F]*$/.test(data)
}

/**
 * 验证文件类型
 * @param {File} file - 文件对象
 * @param {string[]} allowedTypes - 允许的文件类型
 * @returns {boolean} 是否为允许的文件类型
 */
export function isValidFileType(file, allowedTypes = ['.txt']) {
  if (!file || !file.name) return false
  
  const fileName = file.name.toLowerCase()
  return allowedTypes.some(type => fileName.endsWith(type))
}

/**
 * 验证文件大小
 * @param {File} file - 文件对象
 * @param {number} maxSizeInMB - 最大文件大小（MB）
 * @returns {boolean} 是否在允许的大小范围内
 */
export function isValidFileSize(file, maxSizeInMB = 10) {
  if (!file) return false
  
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024
  return file.size <= maxSizeInBytes
} 